#!/usr/bin/env python3
"""
Bin檔案格式化工具
用於將原始bin檔案加上設備類型、功能類型、版本信息和校驗和
"""

import os
import struct
import hashlib
import re
from datetime import datetime
from typing import Dict, List, Tuple

# 設備類型定義 (2 bytes, little endian)
DEVICE_TYPES = {
    'gateway': 0,
    'epd': 1
}

# 功能類型定義 (2 bytes, little endian)
FUNCTION_TYPES = {
    'wifi': 0,
    'ble': 1
}

# 設備支援功能對應表
DEVICE_FUNCTION_SUPPORT = {
    'gateway': ['wifi', 'ble'],
    'epd': ['ble']
}

class BinFormatter:
    def __init__(self):
        self.output_dir = os.path.join(os.path.dirname(__file__), 'output')
        self.ensure_output_dir()
    
    def ensure_output_dir(self):
        """確保輸出目錄存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def validate_version_format(self, version: str) -> bool:
        """驗證版本格式是否為 x.x.x.x"""
        pattern = r'^\d+\.\d+\.\d+\.\d+$'
        return bool(re.match(pattern, version))
    
    def parse_version(self, version: str) -> bytes:
        """將版本字符串轉換為4字節little endian格式"""
        parts = version.split('.')
        if len(parts) != 4:
            raise ValueError("版本格式必須為 x.x.x.x")
        
        try:
            version_bytes = bytes([int(part) for part in parts])
            return version_bytes
        except ValueError:
            raise ValueError("版本號必須為數字")
    
    def calculate_checksum(self, data: bytes) -> bytes:
        """計算bin檔案的校驗和 (CRC32, 4 bytes, little endian)"""
        import zlib
        crc = zlib.crc32(data) & 0xffffffff
        return struct.pack('<I', crc)
    
    def validate_device_function_support(self, device_type: str, function_type: str) -> bool:
        """驗證設備是否支援指定功能"""
        if device_type not in DEVICE_FUNCTION_SUPPORT:
            return False
        return function_type in DEVICE_FUNCTION_SUPPORT[device_type]
    
    def format_bin_file(self, bin_path: str, device_type: str, function_type: str, version: str,
                       model: str, min_hw_version: str = "0.0.0.0",
                       max_hw_version: str = "***************") -> str:
        """
        格式化bin檔案

        Args:
            bin_path: 原始bin檔案路徑
            device_type: 設備類型 (gateway/epd)
            function_type: 功能類型 (wifi/ble)
            version: 韌體版本信息 (x.x.x.x格式)
            model: 模型名稱 (如: GW-001, EPD-001)
            min_hw_version: 最小支援硬體版本 (x.x.x.x格式)
            max_hw_version: 最大支援硬體版本 (x.x.x.x格式)

        Returns:
            輸出檔案路徑
        """
        # 驗證輸入參數
        if not os.path.exists(bin_path):
            raise FileNotFoundError(f"找不到bin檔案: {bin_path}")
        
        if device_type not in DEVICE_TYPES:
            raise ValueError(f"不支援的設備類型: {device_type}")
        
        if function_type not in FUNCTION_TYPES:
            raise ValueError(f"不支援的功能類型: {function_type}")
        
        if not self.validate_version_format(version):
            raise ValueError("版本格式錯誤，必須為 x.x.x.x 格式")

        if not self.validate_version_format(min_hw_version):
            raise ValueError("最小硬體版本格式錯誤，必須為 x.x.x.x 格式")

        if not self.validate_version_format(max_hw_version):
            raise ValueError("最大硬體版本格式錯誤，必須為 x.x.x.x 格式")

        if not self.validate_device_function_support(device_type, function_type):
            raise ValueError(f"{device_type} 不支援 {function_type} 功能")

        # 驗證模型名稱
        if not model or not isinstance(model, str):
            raise ValueError("模型名稱不能為空且必須為字符串")

        if len(model.encode('utf-8')) > 255:
            raise ValueError("模型名稱過長，UTF-8編碼後不能超過255字節")

        # 讀取原始bin檔案
        with open(bin_path, 'rb') as f:
            bin_data = f.read()

        # 準備各個部分的數據
        device_type_bytes = struct.pack('<H', DEVICE_TYPES[device_type])  # 2 bytes

        # 處理模型名稱 - 使用UTF-8編碼
        model_bytes = model.encode('utf-8')
        model_length = len(model_bytes)
        model_length_bytes = struct.pack('<H', model_length)  # 2 bytes

        function_type_bytes = struct.pack('<H', FUNCTION_TYPES[function_type])  # 2 bytes
        version_bytes = self.parse_version(version)  # 4 bytes
        min_hw_version_bytes = self.parse_version(min_hw_version)  # 4 bytes
        max_hw_version_bytes = self.parse_version(max_hw_version)  # 4 bytes
        checksum_bytes = self.calculate_checksum(bin_data)  # 4 bytes

        # 組合新的bin檔案
        # 格式: 2byte(device) + 2byte(model_length) + Nbyte(model) + 2byte(function) + 4byte(version) + 4byte(min_hw) + 4byte(max_hw) + bin_data + 4byte(checksum)
        formatted_data = (device_type_bytes + model_length_bytes + model_bytes + function_type_bytes +
                         version_bytes + min_hw_version_bytes + max_hw_version_bytes +
                         bin_data + checksum_bytes)
        
        # 生成輸出檔案名稱
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        # 將模型名稱中的特殊字符替換為下劃線，以確保檔案名有效
        safe_model = model.replace('/', '_').replace('\\', '_').replace(':', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')
        output_filename = f"{device_type}_{safe_model}_{function_type}_{version}_{timestamp}.bin"
        output_path = os.path.join(self.output_dir, output_filename)
        
        # 寫入輸出檔案
        with open(output_path, 'wb') as f:
            f.write(formatted_data)
        
        return output_path
    
    def get_device_types(self) -> List[str]:
        """獲取支援的設備類型列表"""
        return list(DEVICE_TYPES.keys())
    
    def get_function_types(self) -> List[str]:
        """獲取支援的功能類型列表"""
        return list(FUNCTION_TYPES.keys())
    
    def get_supported_functions(self, device_type: str) -> List[str]:
        """獲取指定設備支援的功能列表"""
        return DEVICE_FUNCTION_SUPPORT.get(device_type, [])

    def parse_bin_file(self, bin_path: str) -> dict:
        """
        解析格式化的bin檔案

        Args:
            bin_path: bin檔案路徑

        Returns:
            解析結果字典
        """
        if not os.path.exists(bin_path):
            raise FileNotFoundError(f"找不到bin檔案: {bin_path}")

        with open(bin_path, 'rb') as f:
            data = f.read()

        if len(data) < 20:  # 最小標頭大小: 2+2+1+2+4+4+4+4 = 23 bytes (假設模型名稱至少1字節)
            raise ValueError("檔案太小，不是有效的格式化bin檔案")

        try:
            # 解析標頭
            offset = 0

            # 設備類型 (2 bytes)
            device_type_code = struct.unpack('<H', data[offset:offset+2])[0]
            offset += 2

            # 模型長度 (2 bytes)
            model_length = struct.unpack('<H', data[offset:offset+2])[0]
            offset += 2

            # 模型名稱 (N bytes)
            if offset + model_length > len(data):
                raise ValueError("模型長度超出檔案範圍")
            model = data[offset:offset+model_length].decode('utf-8')
            offset += model_length

            # 功能類型 (2 bytes)
            function_type_code = struct.unpack('<H', data[offset:offset+2])[0]
            offset += 2

            # 韌體版本 (4 bytes)
            version = '.'.join(str(b) for b in data[offset:offset+4])
            offset += 4

            # 最小硬體版本 (4 bytes)
            min_hw_version = '.'.join(str(b) for b in data[offset:offset+4])
            offset += 4

            # 最大硬體版本 (4 bytes)
            max_hw_version = '.'.join(str(b) for b in data[offset:offset+4])
            offset += 4

            # bin數據和校驗和
            bin_data = data[offset:-4]
            checksum = struct.unpack('<I', data[-4:])[0]

            # 驗證校驗和
            import zlib
            calculated_checksum = zlib.crc32(bin_data) & 0xffffffff
            is_valid = checksum == calculated_checksum

            # 轉換代碼為名稱
            device_type_names = {v: k for k, v in DEVICE_TYPES.items()}
            function_type_names = {v: k for k, v in FUNCTION_TYPES.items()}

            device_type = device_type_names.get(device_type_code, f"unknown_{device_type_code}")
            function_type = function_type_names.get(function_type_code, f"unknown_{function_type_code}")

            return {
                'device_type': device_type,
                'model': model,
                'function_type': function_type,
                'version': version,
                'min_hw_version': min_hw_version,
                'max_hw_version': max_hw_version,
                'bin_data': bin_data,
                'checksum': f"{checksum:08X}",
                'calculated_checksum': f"{calculated_checksum:08X}",
                'is_valid': is_valid,
                'bin_size': len(bin_data),
                'total_size': len(data),
                'header_size': offset,
                'checksum_size': 4
            }

        except (struct.error, UnicodeDecodeError) as e:
            raise ValueError(f"解析bin檔案時發生錯誤: {e}")



def main():
    """主程式入口"""
    formatter = BinFormatter()
    
    print("=== Bin檔案格式化工具 ===")
    print()
    
    # 1. 輸入bin檔案路徑
    while True:
        bin_path = input("請輸入bin檔案路徑: ").strip()
        if os.path.exists(bin_path):
            break
        print("檔案不存在，請重新輸入")
    
    # 2. 選擇設備類型
    print(f"\n支援的設備類型: {', '.join(formatter.get_device_types())}")
    while True:
        device_type = input("請選擇設備類型 (gateway/epd): ").strip().lower()
        if device_type in formatter.get_device_types():
            break
        print("不支援的設備類型，請重新選擇")
    
    # 3. 輸入模型名稱
    while True:
        model = input("請輸入模型名稱 (如: GW-001, EPD-001): ").strip()
        if model:
            break
        print("模型名稱不能為空，請重新輸入")

    # 4. 選擇功能類型
    supported_functions = formatter.get_supported_functions(device_type)
    print(f"\n{device_type} 支援的功能類型: {', '.join(supported_functions)}")
    while True:
        function_type = input(f"請選擇功能類型 ({'/'.join(supported_functions)}): ").strip().lower()
        if function_type in supported_functions:
            break
        print("不支援的功能類型，請重新選擇")

    # 5. 輸入版本信息
    while True:
        version = input("請輸入版本信息 (格式: x.x.x.x): ").strip()
        if formatter.validate_version_format(version):
            break
        print("版本格式錯誤，請使用 x.x.x.x 格式")

    # 6. 輸入最小硬體版本 (可選)
    while True:
        min_hw = input("請輸入最小支援硬體版本 (格式: x.x.x.x，直接按Enter使用預設值0.0.0.0): ").strip()
        if not min_hw:
            min_hw = "0.0.0.0"
            break
        if formatter.validate_version_format(min_hw):
            break
        print("版本格式錯誤，請使用 x.x.x.x 格式")

    # 7. 輸入最大硬體版本 (可選)
    while True:
        max_hw = input("請輸入最大支援硬體版本 (格式: x.x.x.x，直接按Enter使用預設值***************): ").strip()
        if not max_hw:
            max_hw = "***************"
            break
        if formatter.validate_version_format(max_hw):
            break
        print("版本格式錯誤，請使用 x.x.x.x 格式")

    # 8. 處理檔案
    try:
        output_path = formatter.format_bin_file(bin_path, device_type, function_type, version,
                                               model, min_hw, max_hw)
        print(f"\n✅ 處理完成！")
        print(f"輸出檔案: {output_path}")

        # 顯示檔案信息
        file_size = os.path.getsize(output_path)
        print(f"檔案大小: {file_size} bytes")
        print(f"設備類型: {device_type}")
        print(f"模型名稱: {model}")
        print(f"功能類型: {function_type}")
        print(f"韌體版本: {version}")
        print(f"最小硬體版本: {min_hw}")
        print(f"最大硬體版本: {max_hw}")

    except Exception as e:
        print(f"\n❌ 處理失敗: {e}")

if __name__ == "__main__":
    main()
