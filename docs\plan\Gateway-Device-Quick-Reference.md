# Gateway/Device 快速參考手冊

## 🔄 操作流程

```mermaid
flowchart TD
    A[Server Web 新增 Gateway]
    B[點擊鑰匙圖標]
    C[複製 Config 資訊]
    D[貼到 Gateway 設備]
    E[建立 WebSocket 連接]
    F[收到 welcome]
    G[發送 gatewayInfo]
    H[開始 deviceStatus]

    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H

    style A fill:#e1f5fe
    style F fill:#e8f5e8
    style H fill:#e8f5e8
```

## 🚀 快速開始

### 1. 連接流程
```
1. 用戶在 Server Web 新增 Gateway
2. 點擊鑰匙圖標獲取 Config 資訊
3. 將 Config 貼到 Gateway 設備
4. Gateway 建立 WebSocket 連接: ws://server:port/ws?token=JWT_TOKEN
5. 收到 welcome 消息後發送 gatewayInfo
6. 開始定期發送 ping (25秒) 和 deviceStatus (5秒)
```

### 2. 必要的消息類型
- **發送**: `ping`, `gatewayInfo`, `deviceStatus`, `chunk_start_ack`, `chunk_ack`, `chunk_complete_ack`
- **接收**: `welcome`, `pong`, `gatewayInfoAck`, `deviceStatusAck`, `update_preview`, `image_chunk_start`, `image_chunk_complete`
- **二進制**: 分片數據（嵌入式 Index 模式）

## 📨 消息格式速查

### 發送消息 (Gateway → Server)

#### ping (心跳)
```json
{
  "type": "ping",
  "timestamp": 1640995200000
}
```
**頻率**: 每 25 秒

#### gatewayInfo (網關信息)
```json
{
  "type": "gatewayInfo",
  "info": {
    "macAddress": "AA:BB:CC:DD:EE:FF",  // 必須與 Token 中的 MAC 一致
    "model": "Gateway Model 003",
    "hardwareVersion": "*******",
    "wifiFirmwareVersion": "1.0.0",
    "btFirmwareVersion": "2.0.0",
    "ipAddress": "*************",

    // 新增：分片傳輸能力支援
    "chunkingSupport": {
      "enabled": true,                 // 是否支援分片傳輸
      "maxChunkSize": 200,            // 每個分片的最大大小（4 bytes - 512KB）
      "maxSingleMessageSize": 10240,  // 單次 JSON 訊息的最大大小限制（bytes）
      "embeddedIndex": true,          // 是否支援嵌入式 Index 模式
      "jsonHeader": true,             // 是否支援 JSON Header 模式（向後兼容）
      "supportedFormat": "rawdata"    // 偏好的 rawdata 格式：rawdata, runlendata
    }
  }
}
```
**發送時機**: 收到 welcome 後立即發送，之後每 30 秒
**重要**: `chunkingSupport` 決定 Server 是否對該 Gateway 啟用分片傳輸

**分片決策邏輯**:
1. 如果 `rawdata 大小 > maxChunkSize` → 使用分片傳輸
2. 如果 `rawdata 大小 <= maxChunkSize` 但 `JSON 訊息大小 > maxSingleMessageSize` → 使用分片傳輸
3. 否則 → 使用直接傳輸 (`update_preview`)

#### deviceStatus (設備狀態)
```json
{
  "type": "deviceStatus",
  "devices": [
    {
      "macAddress": "11:22:33:44:55:66",
      "model": "EPD-2.9-BW",
      "hardwareVersion": "*******",
      "firmwareVersion": "1.2.3",
      "status": "online",
      "data": {
        "size": "2.9\"",
        "battery": 85,
        "rssi": -65,
        "colorType": "BW",
        "imageCode": "12345678"  // 可選，只有本地有時才包含
      }
    }
  ]
}
```
**頻率**: 每 5 秒
**注意**: 不包含 `dataId`，這是由前端或API控制的欄位

#### chunk_start_ack (分片開始確認)
```json
{
  "type": "chunk_start_ack",
  "chunkId": "chunk_12345",
  "status": "ready",
  "error": null,
  "timestamp": 1640995200000
}
```
**發送時機**: 收到 `image_chunk_start` 後立即發送
**status 值**: `"ready"` (準備就緒) | `"error"` (發生錯誤)

#### chunk_ack (分片確認)
```json
{
  "type": "chunk_ack",
  "chunkId": "chunk_12345",
  "chunkIndex": 5,
  "status": "received",
  "error": null,
  "timestamp": 1640995200000
}
```
**發送時機**: 收到每個分片數據後立即發送
**status 值**: `"received"` (已接收) | `"duplicate"` (重複) | `"error"` (錯誤)

#### chunk_complete_ack (分片完成確認)
```json
{
  "type": "chunk_complete_ack",
  "chunkId": "chunk_12345",
  "status": "success",
  "receivedSize": 9484,
  "error": null,
  "timestamp": 1640995200000
}
```
**發送時機**: 收到 `image_chunk_complete` 後立即發送
**status 值**: `"success"` (成功完成) | `"error"` (重組失敗)

### 接收消息 (Server → Gateway)

#### welcome (歡迎)
```json
{
  "type": "welcome",
  "message": "WebSocket 連接成功",
  "timestamp": 1640995200000,
  "gatewayInfo": {
    "gatewayId": "gateway_id",
    "storeId": "store_id",
    "macAddress": "AA:BB:CC:DD:EE:FF"
  }
}
```
**處理**: 收到後立即發送 gatewayInfo

#### pong (心跳回應)
```json
{
  "type": "pong",
  "timestamp": 1640995200000,
  "serverTime": 1640995200100
}
```

#### update_preview (圖像更新 - 直接傳輸)
```json
{
  "type": "update_preview",
  "deviceMac": "11:22:33:44:55:66",
  "imageData": "data:image/png;base64,iVBORw0KGgo...",
  "imageCode": "87654321",
  "rawdata": [255, 255, 0, 128, 64, ...],  // EPD 原始數據陣列 (Uint8Array)
  "dataType": "runlendata",  // 數據格式類型：rawdata, runlendata 等
  "timestamp": "2021-12-31T16:00:00.000Z"
}
```
**處理**:
- 更新本地 imageCode，下次 deviceStatus 時包含新值
- 根據 `dataType` 處理 `rawdata` 中的數據
- `rawdata` 包含轉換後的 EPD 二進制數據，可直接發送到設備顯示

#### image_chunk_start (分片傳輸開始)
```json
{
  "type": "image_chunk_start",
  "chunkId": "chunk_12345",
  "deviceMac": "11:22:33:44:55:66",
  "imageCode": "87654321",
  "totalChunks": 48,
  "totalSize": 9484,
  "chunkSize": 200,
  "indexSize": 4,
  "dataType": "runlendata",  // 數據格式類型：rawdata, runlendata 等
  "mode": "embedded_index",
  "timestamp": "2021-12-31T16:00:00.000Z"
}
```
**處理**:
1. 準備接收分片數據
2. 記錄 `dataType` 以便後續正確處理數據
3. 發送 `chunk_start_ack` 確認
4. 等待二進制分片數據

#### image_chunk_complete (分片傳輸完成)
```json
{
  "type": "image_chunk_complete",
  "chunkId": "chunk_12345",
  "deviceMac": "11:22:33:44:55:66",
  "imageCode": "87654321",
  "totalChecksum": "a1b2",
  "timestamp": "2021-12-31T16:00:00.000Z"
}
```
**處理**:
1. 驗證所有分片已接收
2. 重組完整數據
3. 更新本地 imageCode
4. 發送 `chunk_complete_ack` 確認

## 📊 數據格式處理

### dataType 格式說明
- **`"rawdata"`**: 未壓縮的原始 EPD 數據，直接使用
- **`"runlendata"`**: 使用 Run-Length Encoding 壓縮的數據，需要解壓縮

### RLE 編碼格式
1. **重複序列** (runLength >= 2):
   - 格式: `[runLength, value]`
   - runLength 範圍: 2-127 (0x02-0x7F)，bit7 = 0

2. **非重複序列** (runLength = 1 或無重複):
   - 格式: `[0x80|length, data...]`
   - length 範圍: 1-127 (0x01-0x7F)，bit7 = 1

**重要**:
- bit7 是最高位元 (MSB)
- 壓縮的只有 EPD 像素數據，不包含 ImageInfo 結構 (12 bytes) 頭部
- 不包含 chunk 的 index 資訊

### 數據處理流程
```python
def process_data(rawdata, data_type):
    if data_type == "rawdata":
        return rawdata  # 直接使用
    elif data_type == "runlendata":
        # 分離 ImageInfo (12 bytes) 和壓縮的像素數據
        image_info = rawdata[:12]
        compressed_pixels = rawdata[12:]
        # 解壓縮像素數據
        decompressed_pixels = decompress_rle(compressed_pixels)
        # 重新組合
        return image_info + decompressed_pixels
```

## ⚠️ 重要注意事項

### 1. MAC 地址安全
- `gatewayInfo` 中的 `macAddress` 必須與 JWT Token 中的完全一致
- 不匹配會導致連線被強制中斷並記錄安全事件

### 2. dataId 和 imageCode 處理
- **dataId**: 不應包含在設備回報中，這是由前端或API控制的欄位
- **imageCode**: 設備回報時不主動包含 `imageCode`
- 只有在收到 Server 圖像更新後才在本地存儲 `imageCode`
- 下次 `deviceStatus` 回報時包含更新後的 `imageCode`

### 3. 分片傳輸機制
- **兩階段決策**: Server 根據 `maxChunkSize` 和 `maxSingleMessageSize` 兩階段判斷是否啟用分片
- **第一階段**: 檢查 rawdata 大小是否超過 `maxChunkSize`
- **第二階段**: 檢查完整 JSON 訊息大小是否超過 `maxSingleMessageSize`
- **嵌入式 Index**: 每個分片前 4 bytes 包含 chunkIndex (little-endian)
- **ACK 機制**: 每個分片必須等待 Gateway 確認後才發送下一個
- **性能警告**: 當分片數量 > 100 時，系統會發出性能警告
- **硬體限制支援**: 支援 4 bytes - 512KB 的分片大小範圍

### 4. 分片數據格式
```
[4 bytes: chunkIndex][N bytes: 實際數據]
```
- **chunkIndex**: 32位無符號整數，little-endian 格式
- **實際數據**: EPD 原始數據的一部分

### 5. status 和 error 參數說明

**status 參數** - 表示操作的執行狀態：
- **分片相關**:
  - `"ready"`: 準備就緒（chunk_start_ack）
  - `"received"`: 已成功接收（chunk_ack）
  - `"duplicate"`: 重複分片（chunk_ack）
  - `"success"`: 操作成功完成（chunk_complete_ack）
  - `"error"`: 操作失敗

**error 參數** - 提供具體錯誤信息：
- 當 `status` 為 `"error"` 時，包含具體的錯誤描述
- 當操作成功時，通常為 `null`
- 幫助調試和錯誤追蹤

### 6. 錯誤處理
```json
{
  "type": "gatewayInfoAck",
  "success": false,
  "fatal": true,  // 如果為 true，連線將被中斷
  "message": "MAC地址不匹配，連線已中斷"
}
```

## 🔧 實作檢查清單

### 連接建立
- [ ] 正確解析 JWT Token
- [ ] 使用正確的 WebSocket URL 格式
- [ ] 處理連接失敗和重連邏輯

### 消息處理
- [ ] 實作所有必要的消息類型
- [ ] 正確的消息發送頻率
- [ ] JSON 格式驗證和錯誤處理
- [ ] 二進制分片數據處理

### 分片傳輸
- [ ] 上報 chunkingSupport 能力
- [ ] 實作 chunk_start_ack、chunk_ack 和 chunk_complete_ack 消息
- [ ] 正確設置 status 和 error 參數
- [ ] 嵌入式 Index 分片數據解析
- [ ] 分片重組和完整性檢查
- [ ] 超時和重傳處理
- [ ] 處理 image_chunk_complete 消息

### 數據格式處理
- [ ] 正確解析 dataType 欄位
- [ ] 實作 RLE 解壓縮算法
- [ ] 正確分離 ImageInfo 和像素數據
- [ ] 處理壓縮數據的重新組合
- [ ] 錯誤處理和數據驗證

### 設備管理
- [ ] 設備掃描和狀態更新
- [ ] imageCode 本地存儲和同步
- [ ] 設備生命週期管理

### 安全性
- [ ] MAC 地址驗證
- [ ] Token 過期處理
- [ ] 錯誤日誌記錄

## ❌ 失敗狀況速查

### 連接階段失敗
| 錯誤 | 狀態碼 | 原因 | 解決方法 |
|------|--------|------|----------|
| Token 驗證失敗 | 401 | Token 無效/過期 | 重新獲取 Config |
| Token 類型錯誤 | 401 | 非 gateway 類型 | 檢查 Token 來源 |
| Gateway ID 不匹配 | 403 | URL 與 Token 不符 | 檢查 Gateway 配置 |
| Store ID 不匹配 | 403 | 門店 ID 不符 | 檢查門店權限 |
| MAC 地址缺失 | 403 | Token 無 MAC | 重新生成 Token |

### 運行階段失敗
| 消息類型 | success | fatal | 錯誤原因 | 處理方式 |
|----------|---------|-------|----------|----------|
| gatewayInfoAck | false | true | MAC 地址不匹配 | 停止重連，檢查配置 |
| gatewayInfoAck | false | false | 網關被刪除 | 重新註冊網關 |
| gatewayInfoAck | false | false | 信息格式錯誤 | 檢查消息格式 |
| deviceStatusAck | false | - | 設備列表無效 | 檢查設備數據 |
| error | - | - | 未知消息類型 | 檢查消息格式 |

### 心跳檢測失敗
| 狀況 | 觸發條件 | 關閉代碼 | 處理方式 |
|------|----------|----------|----------|
| 心跳超時 | >30秒無ping | 1000 | 檢查網絡，重連 |
| 長時間無活動 | >60秒無消息 | 1000 | 檢查程序狀態 |

## 🐛 常見問題

### Q: 為什麼收到 "MAC地址不匹配" 錯誤？
A: 確保 `gatewayInfo` 消息中的 `macAddress` 與 JWT Token 中的完全一致。

### Q: 設備狀態應該包含哪些欄位？
A: 包含 `macAddress`、`model`、`hardwareVersion`、`firmwareVersion`、`status` 和 `data` 對象。不包含 `dataId`（由前端/API控制）。

### Q: 設備狀態什麼時候包含 imageCode？
A: 只有在收到 Server 的 `update_preview` 消息並更新本地 imageCode 後。

### Q: 心跳超時怎麼辦？
A: 檢查網絡連接，確保每 25 秒發送一次 ping 消息。

### Q: 如何處理連接斷開？
A: 實作重連邏輯，等待 5-10 秒後重新嘗試連接。

### Q: fatal: true 錯誤如何處理？
A: 這是致命錯誤，應停止重連並檢查配置，通常是安全問題。

### Q: 如何設定 maxChunkSize？
A: 根據硬體記憶體限制設定，範圍 4 bytes - 512KB。建議：小型設備 200 bytes，中型設備 512 bytes，大型設備 1024 bytes。

### Q: maxSingleMessageSize 和 maxChunkSize 有什麼區別？
A:
- `maxChunkSize`: 控制每個分片的大小，基於硬體記憶體限制
- `maxSingleMessageSize`: 控制單次 JSON 訊息的大小限制，基於網路和處理能力
- 建議 `maxSingleMessageSize` > `maxChunkSize` 以避免過度分片
- 例如：maxChunkSize=200, maxSingleMessageSize=2048

### Q: 什麼情況下會觸發分片傳輸？
A: 兩種情況：
1. rawdata 大小超過 maxChunkSize（第一階段檢查）
2. rawdata 小於 maxChunkSize，但完整的 JSON 訊息（包含 imageData 等）超過 maxSingleMessageSize（第二階段檢查）

### Q: 分片數據如何解析？
A: 前 4 bytes 是 chunkIndex (little-endian)，後續是實際數據。需要按 chunkIndex 順序重組。

### Q: 分片傳輸超時怎麼辦？
A: Server 會自動重傳失敗的分片。Gateway 應確保及時發送 chunk_ack 確認。

### Q: 如何處理分片數量過多的警告？
A: 系統會在分片數量 > 100 時發出警告。可以增加 maxChunkSize 或優化圖像大小來減少分片數量。

### Q: status 和 error 參數什麼時候使用？
A: 在所有分片相關的 ACK 消息中都需要包含 status 參數。當 status 為 "error" 時，必須在 error 參數中提供具體的錯誤描述。

### Q: 如何正確設置分片 ACK 的 status？
A:
- chunk_start_ack: 使用 "ready" 表示準備接收，"error" 表示準備失敗
- chunk_ack: 使用 "received" 表示新分片，"duplicate" 表示重複分片，"error" 表示處理失敗
- chunk_complete_ack: 使用 "success" 表示重組成功，"error" 表示重組失敗

### Q: 如何處理不同的 dataType？
A: 根據 dataType 欄位處理數據：
- "rawdata": 直接使用，無需額外處理
- "runlendata": 需要解壓縮，分離 ImageInfo (12 bytes) 和壓縮的像素數據，解壓縮後重新組合

### Q: RLE 解壓縮失敗怎麼辦？
A: 檢查：
- 是否正確分離了 ImageInfo 和像素數據
- 解壓縮算法是否正確實現 bit7 判斷
- 數據是否在傳輸過程中損壞

### Q: 為什麼不壓縮 ImageInfo 結構？
A: ImageInfo 包含重要的設備參數（X, Y, Width, Height, imageCode），必須保持原始格式以確保設備正確解析。只有像素數據部分才進行壓縮。

## 📋 測試步驟

1. **連接測試**
   ```bash
   # 使用測試腳本驗證連接
   node server/tests/ws-client-from-copied-info.js
   ```

2. **消息格式測試**
   - 驗證所有發送消息的 JSON 格式
   - 檢查必要字段是否完整

3. **錯誤處理測試**
   - 故意發送錯誤的 MAC 地址
   - 測試網絡中斷恢復

4. **性能測試**
   - 監控消息發送頻率
   - 檢查內存和 CPU 使用率

## 🔗 相關文檔

- [完整實作指南](./Gateway-Device-Implementation-Guide.md)
- [WebSocket 安全增強](../server/services/websocket-security-enhancement.md)
- [測試腳本說明](../server/tests/ws-client-from-copied-info-修正說明.md)

---

**最後更新**: 2024年1月
**版本**: 2.2.0 - 分片決策邏輯增強
**新功能**:
- 嵌入式 Index 分片傳輸
- Gateway 能力上報機制
- 硬體限制支援 (4 bytes - 512KB)
- 性能警告系統
- **dataType 欄位統一**: 使用 `dataType` 取代 `rawdataFormat`
- **RLE 壓縮支援**: 完整的 Run-Length Encoding 實作指南
- **數據處理流程**: 詳細的壓縮數據處理說明
- **兩階段分片決策**: 新增 `maxSingleMessageSize` 參數，支援更智能的分片決策
- **JSON 訊息大小檢查**: 當 rawdata 小但 JSON 訊息大時自動切換到分片傳輸
