// WebSocket 測試客戶端 (從複製的WebSocket資訊啟動)
const WebSocket = require('ws');
const readline = require('readline');
const fs = require('fs');
const path = require('path');

// Rawdata 格式常數
const RAWDATA_FORMATS = {
  RAWDATA: 'rawdata',
  RUNLENDATA: 'runlendata'
};

// 用於存儲設備的 imageCode，模擬真實網關的行為
const deviceImageCodes = {};

// Run-Length 壓縮函數（與 Go 版本輸出相同）
function compressRunLength(buf) {
  const result = [];
  const n = buf.length;
  let inx = 0;

  while (inx < n) {
    // 檢查重複序列
    let runLength = 1;
    while (inx + runLength < n && buf[inx + runLength] === buf[inx] && runLength < 0x7F) {
      runLength++;
    }

    if (runLength >= 2) {
      // 編碼重複序列：[runLength, value] (bit7 = 0)
      result.push(runLength);
      result.push(buf[inx]);
      inx += runLength;
    } else {
      // 非重複序列
      const start = inx;
      while (inx < n &&
             (inx + 1 >= n || buf[inx] !== buf[inx + 1]) &&
             (inx - start) < 0x7F) {
        inx++;
      }
      const length = inx - start;
      result.push(0x80 | length); // bit7 = 1
      for (let i = start; i < inx; i++) {
        result.push(buf[i]);
      }
    }
  }

  return new Uint8Array(result);
}

// Run-Length 解壓縮函數（與 Go 版本對應）
function decompressRunLength(compressedData) {
  const decompressed = [];
  let i = 0;

  while (i < compressedData.length) {
    const header = compressedData[i];
    i++;

    if ((header & 0x80) === 0) {
      // 重複序列：bit7 = 0
      const runLength = header;
      if (i >= compressedData.length) {
        throw new Error('Incomplete RLE data: missing value byte');
      }
      const value = compressedData[i];
      i++;

      for (let j = 0; j < runLength; j++) {
        decompressed.push(value);
      }
    } else {
      // 非重複序列：bit7 = 1
      const length = header & 0x7F;
      if (i + length > compressedData.length) {
        throw new Error('Incomplete RLE data: insufficient data bytes');
      }

      for (let j = 0; j < length; j++) {
        decompressed.push(compressedData[i + j]);
      }
      i += length;
    }
  }

  return new Uint8Array(decompressed);
}

// 通用解壓縮函數
function decompressRawdata(rawdata, format) {
  switch (format) {
    case RAWDATA_FORMATS.RAWDATA:
      return rawdata; // 無需解壓縮
    case RAWDATA_FORMATS.RUNLENDATA:
      // RLE 壓縮數據，需要正確處理 ImageInfo 和像素數據
      const rawdataBytes = rawdata instanceof Uint8Array ? rawdata : new Uint8Array(rawdata);

      if (rawdataBytes.length < 12) {
        throw new Error('Rawdata too small to contain ImageInfo (12 bytes)');
      }

      // 分離 ImageInfo (前 12 bytes) 和壓縮的像素數據
      const imageInfo = rawdataBytes.slice(0, 12);
      const compressedPixels = rawdataBytes.slice(12);

      // 解壓縮像素數據
      const decompressedPixels = decompressRunLength(compressedPixels);

      // 重新組合完整數據
      const completeData = new Uint8Array(12 + decompressedPixels.length);
      completeData.set(imageInfo, 0);
      completeData.set(decompressedPixels, 12);

      return completeData;
    default:
      console.warn(`未知的 rawdata 格式: ${format}，當作未壓縮處理`);
      return rawdata;
  }
}

// 分片接收器類別
class ChunkReceiver {
  constructor(ws) {
    this.ws = ws;
    this.receivedChunks = new Map();    // 追蹤已接收的分片
    this.chunkBuffer = new Map();       // 儲存分片資料
    this.currentChunkId = null;         // 當前傳輸的 chunkId
    this.expectedTotalChunks = 0;       // 預期總分片數
    this.duplicateCount = 0;            // 重複分片計數
    this.receivedCount = 0;             // 已接收分片計數
    this.deviceMac = null;              // 設備MAC地址
    this.imageCode = null;              // 圖片代碼
    this.expectedFormat = 'rawdata';    // 新增：預期的數據格式
    // 失敗模擬相關屬性
    this.simulateFailure = false;           // 單次失敗模式
    this.globalSimulateFailure = false;     // 全局失敗模式
  }

  // 處理分片開始訊息
  async handleChunkStart(message) {
    this.cleanup();

    this.currentChunkId = message.chunkId;
    this.expectedTotalChunks = message.totalChunks;
    this.deviceMac = message.deviceMac;
    this.imageCode = message.imageCode;
    this.expectedFormat = message.dataType || 'rawdata'; // 記錄格式

    console.log(`📦 開始接收分片: ${message.chunkId}, 總分片數: ${message.totalChunks}, 設備: ${this.deviceMac}, 格式: ${this.expectedFormat}`);

    // 發送開始確認
    this.sendStartAck(message.chunkId, 'ready');
  }

  // 處理二進制分片數據（嵌入式Index）
  async handleBinaryChunkData(binaryData) {
    if (binaryData.length < 4) {
      console.error('❌ 收到的分片數據太小，無法包含index');
      return;
    }

    // 解析前4bytes獲得chunkIndex
    const indexView = new DataView(binaryData.buffer, binaryData.byteOffset, 4);
    const chunkIndex = indexView.getUint32(0, true); // little-endian

    // 提取實際數據（跳過前4bytes的index）
    const actualData = new Uint8Array(binaryData.buffer, binaryData.byteOffset + 4, binaryData.length - 4);

    console.log(`📥 收到分片 ${chunkIndex}: ${actualData.length} bytes 數據 + 4 bytes index`);

    // 檢查是否要模擬失敗（全局模式或單次模式）
    if (this.globalSimulateFailure || this.simulateFailure) {
      // 計算失敗點（每次都重新計算，因為不同傳輸可能有不同的分片數）
      const currentFailurePoint = Math.ceil(this.expectedTotalChunks / 2);

      // 如果是第一個分片，顯示模擬信息
      if (chunkIndex === 0) {
        console.log(`🧪 開始模擬分片傳輸失敗 - 將接收前 ${currentFailurePoint} 個分片，然後一直回報中間值`);
      }

      // 檢查當前分片索引是否已經達到失敗點
      if (chunkIndex >= currentFailurePoint) {
        // 達到失敗點後，一直發送中間值的 ACK，讓 server 檢測到進度停滯
        const middleIndex = currentFailurePoint - 1; // 使用中間值作為停滯點

        console.log(`🛑 模擬傳輸失敗 - 收到分片 ${chunkIndex}，但回報進度停滯在 ${middleIndex}`);
        console.log(`📊 不儲存分片 ${chunkIndex}，向 server 回報停滯在分片 ${middleIndex}`);

        // 發送中間值的 ACK，讓 server 認為進度停滯
        this.sendAck(this.currentChunkId, middleIndex, 'received');

        // 如果是單次模式，重置失敗標記
        if (this.simulateFailure && !this.globalSimulateFailure) {
          this.simulateFailure = false;
          console.log('🔄 已重置單次失敗模式');
        }

        return; // 完全停止處理，不儲存分片，讓 server 檢測到進度停滯
      }
    }

    // 重複檢測
    if (this.receivedChunks.has(chunkIndex)) {
      console.log(`🔄 檢測到重複分片: ${chunkIndex}`);
      this.duplicateCount++;
      this.sendAck(this.currentChunkId, chunkIndex, 'duplicate');
      return;
    }

    // 處理新分片（只有在非失敗模式下才會執行到這裡）
    try {
      // 儲存分片資料到緩衝區
      this.chunkBuffer.set(chunkIndex, new Uint8Array(actualData));

      // 記錄接收狀態
      this.receivedChunks.set(chunkIndex, {
        received: true,
        timestamp: new Date().toISOString(),
        size: actualData.length
      });

      this.receivedCount++;
      console.log(`✅ 分片 ${chunkIndex} 已儲存，進度: ${this.receivedCount}/${this.expectedTotalChunks}`);

      // 回應成功確認
      this.sendAck(this.currentChunkId, chunkIndex, 'received');

      // 檢查是否接收完成
      if (this.receivedCount === this.expectedTotalChunks) {
        console.log(`🎯 所有分片接收完成，開始重組圖片`);
        await this.reassembleImage();
      }

    } catch (error) {
      console.error(`❌ 處理分片 ${chunkIndex} 時出錯:`, error);
      this.sendAck(this.currentChunkId, chunkIndex, 'error', error.message);
    }
  }

  // 處理分片完成訊息
  async handleChunkComplete(message) {
    console.log(`📊 分片傳輸統計: ${this.receivedCount} 接收, ${this.duplicateCount} 重複`);

    // 發送完成確認
    this.sendCompleteAck(message.chunkId, 'success');
  }

  // 重組完整圖片
  async reassembleImage() {
    try {
      // 按順序重組分片
      const totalSize = Array.from(this.chunkBuffer.values())
        .reduce((sum, chunk) => sum + chunk.length, 0);

      const completeData = new Uint8Array(totalSize);
      let offset = 0;

      for (let i = 0; i < this.expectedTotalChunks; i++) {
        const chunkData = this.chunkBuffer.get(i);
        if (!chunkData) {
          throw new Error(`缺少分片: ${i}`);
        }

        completeData.set(chunkData, offset);
        offset += chunkData.length;
      }

      console.log(`🎯 圖片重組完成: ${totalSize} bytes, 格式: ${this.expectedFormat}`);

      // 保存重組後的圖片
      await this.saveReassembledData(completeData, this.deviceMac, this.imageCode, this.expectedFormat);

    } catch (error) {
      console.error(`❌ 重組失敗:`, error);
    }
  }

  // 發送各種ACK回應
  sendStartAck(chunkId, status, error = null) {
    const ackMessage = {
      type: 'chunk_start_ack',
      chunkId: chunkId,
      status: status,
      error: error,
      timestamp: new Date().toISOString()
    };
    console.log(`📤 發送開始ACK: ${status}`);
    this.ws.send(JSON.stringify(ackMessage));
  }

  sendAck(chunkId, chunkIndex, status, error = null) {
    // 在失敗模式下，回報的 receivedCount 應該對應到回報的 chunkIndex
    let reportedReceivedCount = this.receivedCount;
    if ((this.globalSimulateFailure || this.simulateFailure) && status === 'received') {
      // 如果是模擬失敗模式且回報中間值，receivedCount 應該是 chunkIndex + 1
      reportedReceivedCount = chunkIndex + 1;
    }

    const ackMessage = {
      type: 'chunk_ack',
      chunkId: chunkId,
      chunkIndex: chunkIndex,
      status: status,
      error: error,
      timestamp: new Date().toISOString(),
      receivedCount: reportedReceivedCount,
      duplicateCount: this.duplicateCount
    };
    console.log(`📤 發送分片ACK: chunk ${chunkIndex}, status: ${status}, reportedCount: ${reportedReceivedCount}`);
    this.ws.send(JSON.stringify(ackMessage));
  }

  sendCompleteAck(chunkId, status, error = null) {
    const ackMessage = {
      type: 'chunk_complete_ack',
      chunkId: chunkId,
      status: status,
      receivedSize: Array.from(this.chunkBuffer.values()).reduce((sum, chunk) => sum + chunk.length, 0),
      error: error,
      timestamp: new Date().toISOString()
    };
    console.log(`📤 發送完成ACK: ${status}`);
    this.ws.send(JSON.stringify(ackMessage));
  }

  // 保存重組後的數據
  async saveReassembledData(rawBuffer, deviceMac, imageCode, format = 'rawdata') {
    try {
      console.log(`準備保存重組後的 ${format} 數據為 bin 檔案，裝置 MAC: ${deviceMac}`);

      // 檢查 buffer 是否有效
      if (!Buffer.isBuffer(rawBuffer) && !(rawBuffer instanceof Uint8Array)) {
        console.warn('無效的原始數據 buffer，跳過保存');
        return;
      }

      // 確保是 Buffer 格式
      const finalBuffer = Buffer.isBuffer(rawBuffer) ? rawBuffer : Buffer.from(rawBuffer);

      if (finalBuffer.length === 0) {
        console.warn('原始數據 buffer 為空，跳過保存');
        return;
      }

      // 建立保存原始數據的目錄
      const path = require('path');
      const fs = require('fs');
      const saveDir = path.join(__dirname, 'saved_images');
      if (!fs.existsSync(saveDir)) {
        fs.mkdirSync(saveDir, { recursive: true });
      }

      // 根據格式調整檔案名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `${format}_chunked_${deviceMac.replace(/:/g, '')}_${timestamp}.bin`;
      const filePath = path.join(saveDir, fileName);

      // 寫入文件
      fs.writeFileSync(filePath, finalBuffer);

      console.log(`已成功將重組後的 ${format} 數據保存到: ${filePath}`);
      console.log(`數據大小: ${finalBuffer.length} 字節`);

      // 如果是壓縮格式，嘗試解壓縮驗證
      if (format !== 'rawdata') {
        try {
          const decompressed = decompressRawdata(finalBuffer, format);
          console.log(`解壓縮驗證成功，解壓縮後大小: ${decompressed.length} 字節`);

          if (finalBuffer.length < decompressed.length) {
            const compressionRatio = (finalBuffer.length / decompressed.length * 100).toFixed(1);
            console.log(`壓縮比: ${compressionRatio}%`);
          }

          // 保存解壓縮後的數據
          const decompressedFileName = `rawdata_decompressed_chunked_${deviceMac.replace(/:/g, '')}_${timestamp}.bin`;
          const decompressedFilePath = path.join(saveDir, decompressedFileName);
          fs.writeFileSync(decompressedFilePath, Buffer.from(decompressed));
          console.log(`解壓縮數據已保存到: ${decompressedFilePath}`);
        } catch (decompressError) {
          console.error('解壓縮驗證失敗:', decompressError.message);
        }
      }

      // 如果提供了 imageCode，記錄關聯
      if (imageCode) {
        console.log(`關聯的 imageCode: ${imageCode}`);

        // 保存到裝置 imageCode 集合中
        deviceImageCodes[deviceMac] = imageCode;
        console.log(`已將 imageCode ${imageCode} 儲存至本地變數，將在下次發送裝置狀態時使用`);
      }

      // 顯示原始數據的前幾個字節（用於調試）
      const previewBytes = finalBuffer.subarray(0, Math.min(16, finalBuffer.length));
      console.log(`重組數據前 ${previewBytes.length} 字節 (hex): ${Buffer.from(previewBytes).toString('hex')}`);

    } catch (error) {
      console.error('保存重組後的原始數據失敗:', error);
    }
  }

  // 清理資源
  cleanup() {
    this.receivedChunks.clear();
    this.chunkBuffer.clear();
    this.duplicateCount = 0;
    this.receivedCount = 0;
    this.currentChunkId = null;
    this.expectedTotalChunks = 0;
    this.deviceMac = null;
    this.imageCode = null;
    this.expectedFormat = 'rawdata'; // 重置格式
    // 注意：不重置 globalSimulateFailure，因為它是全局設定
  }
}

// 建立命令行界面
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 提示用戶輸入
const prompt = (query) => new Promise((resolve) => rl.question(query, resolve));

// 建立 WebSocket 連接
function connectWebSocket(wsInfo, maxChunkSize = 20, maxSingleMessageSize = 2048) {
  // 用於存儲用戶添加的自定義設備
  const customDevices = [];

  // 生成三個固定的設備MAC地址（在連接建立時生成一次）
  const device1Mac = generateRandomMac();
  const device2Mac = generateRandomMac();
  const device3Mac = generateRandomMac();

  // 初始化預設設備的 imageCode（如果尚未設置）
  [device1Mac, device2Mac, device3Mac].forEach(mac => {
    if (!deviceImageCodes[mac]) {
      deviceImageCodes[mac] = Math.floor(Math.random() * 100000000).toString();
    }
  });

  // 構建 WebSocket URL
  const url = wsInfo.url + (wsInfo.url.includes('?') ? '&' : '?') + 'token=' + wsInfo.token;
  console.log(`嘗試連接到 WebSocket 服務器: ${url}`);

  const ws = new WebSocket(url);
  let pingInterval, deviceStatusInterval, gatewayInfoTimeout;

  // 分片接收狀態管理
  let chunkReceiver = null;
  let globalSimulateFailure = false; // 全局失敗模式設定

  // 清理定時器
  function clearIntervals() {
    if (pingInterval) clearInterval(pingInterval);
    if (deviceStatusInterval) clearInterval(deviceStatusInterval);
    if (gatewayInfoTimeout) clearInterval(gatewayInfoTimeout);
  }

  ws.on('open', () => {
    console.log('連接已建立');

    // 發送 ping 消息
    const pingMessage = {
      type: 'ping',
      timestamp: Date.now()
    };

    const pingMessageStr = JSON.stringify(pingMessage);
    console.log('\n===== 發送消息 =====');
    console.log('類型: ping 消息');
    console.log('內容:', pingMessage);
    console.log('JSON格式:', pingMessageStr);
    console.log('=====================\n');
    ws.send(pingMessageStr);

    // 注意：網關信息消息將在收到welcome消息後發送

    // 定期發送心跳 (ping) 消息，確保連接保持活躍
    pingInterval = setInterval(() => {
      const pingMessage = {
        type: 'ping',
        timestamp: Date.now()
      };
      const pingMessageStr = JSON.stringify(pingMessage);
      console.log('\n===== 發送消息 =====');
      console.log('類型: 定期 ping 消息');
      console.log('內容:', pingMessage);
      console.log('JSON格式:', pingMessageStr);
      console.log('=====================\n');
      ws.send(pingMessageStr);
    }, 25000);  // 設置為25秒，低於服務器端的30秒心跳檢查

    // 顯示命令幫助
    function showHelp() {
      console.log('\n可用命令:');
      console.log('  help - 顯示此幫助信息');
      console.log('  q - 退出程序');
      console.log('  add - 添加自定義設備');
      console.log('  list - 列出所有當前模擬的設備');
      console.log('  remove <序號> - 移除指定序號的自定義設備');
      console.log('  request-image <裝置MAC> - 請求特定裝置的預覽圖像');
      console.log('  test-chunk-fail - 啟用全局分片失敗模式（所有設備）');
      console.log('  reset-chunk-fail - 重置分片失敗模式為正常');
      console.log('\n說明:');
      console.log('  - 設備回報不包含 dataId（由前端或API控制的欄位）');
      console.log('  - 設備回報不包含 imageCode（符合 server 架構）');
      console.log('  - 當收到 server 的圖像更新時，會自動更新本地 imageCode');
      console.log('  - 下次設備狀態回報時會包含更新後的 imageCode');
      console.log('  - test-chunk-fail 會讓所有設備的分片傳輸模擬失敗（接收一半後停止）');
      console.log('  - 這會觸發 server 超時並回退到 update_preview 機制');
      console.log('  - reset-chunk-fail 會恢復正常的分片接收處理');
    }

    // 添加自定義設備
    async function addCustomDevice() {
      try {
        const mac = await prompt('請輸入設備 MAC 地址 (格式如 AA:BB:CC:DD:EE:FF): ');
        if (!mac.match(/^([0-9A-Fa-f]{2}[:]){5}([0-9A-Fa-f]{2})$/)) {
          console.log('錯誤: MAC 地址格式不正確，請使用格式 AA:BB:CC:DD:EE:FF');
          return;
        }

        const status = (await prompt('請輸入設備狀態 (online/offline，默認 online): ')) || 'online';
        const size = await prompt('請輸入設備尺寸 (例如 2.9", 4.2"，默認 2.9"): ') || '2.9"';
        const imageCode = await prompt('請輸入設備 imageCode (可選，用於模擬設備回報): ') || '';
        const colorType = await prompt('請輸入設備顏色類型 (BW/BWR/BWRY，默認 BW): ') || 'BW';

        // 如果用戶提供了 imageCode，存儲到本地映射中
        if (imageCode) {
          deviceImageCodes[mac] = imageCode;
          console.log(`已將 imageCode ${imageCode} 存儲到設備 ${mac} 的本地映射中`);
        }

        // 創建自定義設備（不包含 dataId 和 imageCode，符合 server 架構要求）
        const customDevice = {
          macAddress: mac,
          status: status,
          // 注意：不包含 dataId，因為這是由前端或API控制的欄位，不是由裝置自己回報的欄位
          data: {
            size: size,
            battery: Math.floor(Math.random() * 100),
            rssi: -1 * Math.floor(Math.random() * 100),
            // 注意：不包含 imageCode，因為裝置回報不應該包含此字段
            colorType: colorType
          }
        };

        // 添加到自定義設備列表
        customDevices.push(customDevice);
        console.log(`成功添加自定義設備: MAC=${mac}, 尺寸=${size}`);
        console.log(`目前共有 ${customDevices.length} 個自定義設備`);
      } catch (err) {
        console.error('添加設備時出錯:', err.message);
      }
    }

    // 列出所有設備
    function listDevices() {
      console.log('\n設備列表:');
      console.log('預設設備:');
      console.log(`1. 2.9吋 BWR 設備 - MAC: ${device1Mac}, 狀態: online`);
      console.log(`   本地 imageCode: ${deviceImageCodes[device1Mac] || 'N/A'}`);
      console.log(`2. 6吋 BW 設備 - MAC: ${device2Mac}, 狀態: online`);
      console.log(`   本地 imageCode: ${deviceImageCodes[device2Mac] || 'N/A'}`);
      console.log(`3. 3.7吋 BWRY 設備 - MAC: ${device3Mac}, 狀態: online`);
      console.log(`   本地 imageCode: ${deviceImageCodes[device3Mac] || 'N/A'}`);

      if (customDevices.length === 0) {
        console.log('尚未添加任何自定義設備');
      } else {
        console.log('自定義設備:');
        customDevices.forEach((device, index) => {
          console.log(`${index + 4}. MAC: ${device.macAddress}, 狀態: ${device.status}`);
          console.log(`   尺寸: ${device.data?.size || 'N/A'}, 本地 imageCode: ${deviceImageCodes[device.macAddress] || 'N/A'}, colorType: ${device.data?.colorType || 'N/A'}`);
        });
      }
    }

    // 移除自定義設備
    function removeDevice(index) {
      const deviceIndex = parseInt(index) - 4; // 調整索引（因為有3個預設設備，索引從4開始）
      if (isNaN(deviceIndex) || deviceIndex < 0 || deviceIndex >= customDevices.length) {
        console.log('錯誤: 無效的設備序號');
        listDevices(); // 顯示正確的設備列表供參考
        return;
      }

      const removedDevice = customDevices.splice(deviceIndex, 1)[0];
      console.log(`已移除設備: MAC=${removedDevice.macAddress}`);
    }

    // 請求設備圖片預覽
    function requestDeviceImage(macAddress) {
      if (!macAddress || !macAddress.match(/^([0-9A-Fa-f]{2}[:]){5}([0-9A-Fa-f]{2})$/)) {
        console.log('錯誤: MAC 地址格式不正確，請使用格式 AA:BB:CC:DD:EE:FF');
        return;
      }

      // 發送請求圖片預覽的消息
      const requestImageMessage = {
        type: 'requestPreviewImage',
        macAddress: macAddress,
        timestamp: Date.now()
      };

      const requestImageMessageStr = JSON.stringify(requestImageMessage);
      console.log('\n===== 發送消息 =====');
      console.log(`類型: 圖片預覽請求`);
      console.log('內容:');
      console.log(JSON.stringify(requestImageMessage, null, 2));
      console.log('JSON格式:');
      console.log(requestImageMessageStr);
      console.log('=====================\n');

      ws.send(requestImageMessageStr);
    }

    // 測試 chunk 失敗後的 update_preview 功能
    function testChunkFailure() {
      console.log('\n🧪 開始測試 chunk 失敗後的 update_preview 功能');
      console.log(`當前 maxSingleMessageSize 限制: 2048 bytes（已設定較小值以便測試）`);

      // 設定全局分片接收失敗模式
      console.log('\n步驟 1: 設定全局分片接收失敗模式');
      globalSimulateFailure = true;

      // 如果已有分片接收器，也同步設定
      if (chunkReceiver) {
        chunkReceiver.globalSimulateFailure = true;
      }

      console.log('✅ 已設定全局失敗模式，所有新的分片傳輸都會模擬失敗');

      console.log('\n步驟 2: 現在可以請求任何設備的圖片預覽來測試');
      console.log('可用的測試設備:');
      console.log(`- ${device1Mac} (2.9" BWR)`);
      console.log(`- ${device2Mac} (4.2" BW)`);
      console.log(`- ${device3Mac} (7.5" BWR)`);

      // 顯示自定義設備
      if (customDevices.length > 0) {
        console.log('- 自定義設備:');
        customDevices.forEach((device, index) => {
          console.log(`  ${index + 1}. ${device.macAddress} (${device.size} ${device.colorType})`);
        });
      }

      console.log('\n使用方法:');
      console.log('request-image <設備MAC> - 請求指定設備的圖片預覽');
      console.log('例如: request-image ' + device1Mac);

      console.log('\n說明:');
      console.log('- 現在所有設備的分片接收都會模擬傳輸失敗');
      console.log('- 客戶端會接收一半數量的分片，然後一直回報中間值 ACK');
      console.log('- 這會讓 server 檢測到進度停滯，觸發失敗處理');
      console.log('- server 會回退到 update_preview 機制');
      console.log('- 如果 update_preview JSON 訊息過大，會被 maxSingleMessageSize 限制拒絕');
      console.log('- 被拒絕的任務會記錄到設備和 gateway 事件中');
      console.log('- 輸入 "reset-chunk-fail" 可以重置失敗模式');
      console.log('- 請觀察後續的日誌輸出來驗證功能是否正常\n');
    }

    // 重置 chunk 失敗模式
    function resetChunkFailure() {
      console.log('\n🔄 重置分片接收失敗模式');
      globalSimulateFailure = false;

      // 如果已有分片接收器，也同步重置
      if (chunkReceiver) {
        chunkReceiver.globalSimulateFailure = false;
      }

      console.log('✅ 已重置全局失敗模式為正常');
      console.log('現在所有分片接收將正常處理\n');
    }

    // 顯示初始幫助信息
    console.log('\n連接成功！您可以輸入命令來管理模擬設備。');
    showHelp();

    // 監聽用戶輸入
    rl.on('line', async (input) => {
      const command = input.trim();

      if (command === 'q') {
        console.log('正在關閉連接並退出...');
        clearIntervals();
        ws.close();
        setTimeout(() => process.exit(0), 1000);
      } else if (command === 'help') {
        showHelp();
      } else if (command === 'add') {
        await addCustomDevice();
      } else if (command === 'list') {
        listDevices();
      } else if (command.startsWith('remove ')) {
        const index = command.split(' ')[1];
        removeDevice(index);
      } else if (command.startsWith('request-image ')) {
        const macAddress = command.split(' ')[1];
        requestDeviceImage(macAddress);
      } else if (command === 'test-chunk-fail') {
        testChunkFailure();
      } else if (command === 'reset-chunk-fail') {
        resetChunkFailure();
      } else if (command) {
        console.log('未知命令。輸入 help 顯示可用命令。');
      }
    });

    // 每 5 秒發送一次設備狀態消息
    deviceStatusInterval = setInterval(() => {
      // 設備1: 2.9吋 BWR
      const device1 = {
        macAddress: device1Mac,
        model: 'EPD-2.9-BWR',
        hardwareVersion: '*******',
        firmwareVersion: '1.2.3',
        status: 'online',
        // 注意：不包含 dataId，因為這是由前端或API控制的欄位，不是由裝置自己回報的欄位
        data: {
          size: '2.9"',
          battery: Math.floor(Math.random() * 100),
          rssi: -1 * Math.floor(Math.random() * 100),
          colorType: 'BWR'
        }
      };

      // 設備2: 6吋 BW
      const device2 = {
        macAddress: device2Mac,
        model: 'EPD-6.0-BW',
        hardwareVersion: '*******',
        firmwareVersion: '1.3.1',
        status: 'online',
        // 注意：不包含 dataId，因為這是由前端或API控制的欄位，不是由裝置自己回報的欄位
        data: {
          size: '6"',
          battery: Math.floor(Math.random() * 100),
          rssi: -1 * Math.floor(Math.random() * 100),
          colorType: 'BW'
        }
      };

      // 設備3: 3.7吋 BWRY
      const device3 = {
        macAddress: device3Mac,
        model: 'EPD-3.7-BWRY',
        hardwareVersion: '*******',
        firmwareVersion: '1.1.5',
        status: 'online',
        // 注意：不包含 dataId，因為這是由前端或API控制的欄位，不是由裝置自己回報的欄位
        data: {
          size: '3.7"',
          battery: Math.floor(Math.random() * 100),
          rssi: -1 * Math.floor(Math.random() * 100),
          colorType: 'BWRY'
        }
      };

      // 如果有本地存儲的 imageCode，添加到設備數據中
      [device1, device2, device3].forEach(device => {
        if (deviceImageCodes[device.macAddress]) {
          device.data.imageCode = deviceImageCodes[device.macAddress];
        }
      });

      // 處理自定義設備，添加本地存儲的 imageCode
      const processedCustomDevices = customDevices.map(device => {
        const processedDevice = { ...device };
        if (deviceImageCodes[device.macAddress]) {
          processedDevice.data = {
            ...processedDevice.data,
            imageCode: deviceImageCodes[device.macAddress]
          };
        }
        return processedDevice;
      });

      // 合併預設設備和處理後的自定義設備
      const allDevices = [device1, device2, device3, ...processedCustomDevices];

      const deviceStatusMessage = {
        type: 'deviceStatus',
        devices: allDevices
      };

      const deviceStatusMessageStr = JSON.stringify(deviceStatusMessage);
      console.log('\n===== 發送消息 =====');
      console.log(`類型: 設備狀態消息 (共 ${allDevices.length} 個設備)`);
      console.log('內容:');
      console.log(JSON.stringify(deviceStatusMessage, null, 2));
      console.log('JSON格式:');
      console.log(deviceStatusMessageStr);
      console.log('=====================\n');

      // 如果有自定義設備，顯示詳細信息
      if (customDevices.length > 0) {
        console.log('設備列表:');
        allDevices.forEach((device, index) => {
          console.log(`  ${index + 1}. MAC: ${device.macAddress}, 狀態: ${device.status}`);
          console.log(`     型號: ${device.model || 'N/A'}, 硬體版本: ${device.hardwareVersion || 'N/A'}, 韌體版本: ${device.firmwareVersion || 'N/A'}`);
          console.log(`     尺寸: ${device.data?.size || 'N/A'}, 回報 imageCode: ${device.data?.imageCode || 'N/A'}, colorType: ${device.data?.colorType || 'N/A'}`);
        });
      }

      ws.send(deviceStatusMessageStr);
    }, 5000);

    // 每 30 秒重新發送一次網關信息消息
    gatewayInfoTimeout = setInterval(() => {
      const gatewayInfoMessage = {
        type: 'gatewayInfo',
        info: {
          macAddress: wsInfo.macAddress || 'AA:BB:CC:DD:EE:FF',
          model: wsInfo.model || 'Gateway Model 003',
          hardwareVersion: wsInfo.hardwareVersion || '*******',
          wifiFirmwareVersion: wsInfo.wifiFirmwareVersion || '1.0.0',
          btFirmwareVersion: wsInfo.btFirmwareVersion || '2.0.0',
          ipAddress: wsInfo.ipAddress || '*************',
          chunkingSupport: {
            enabled: true,
            maxChunkSize: maxChunkSize,
            maxSingleMessageSize: 2048,
            embeddedIndex: true,
            jsonHeader: true,
            supportedFormat: wsInfo.preferredFormat || 'rawdata'
          }
        }
      };

      const gatewayInfoMessageStr = JSON.stringify(gatewayInfoMessage);
      console.log('\n===== 發送消息 =====');
      console.log('類型: 網關信息消息 (定期更新)');
      console.log('內容:');
      console.log(JSON.stringify(gatewayInfoMessage, null, 2));
      console.log('JSON格式:');
      console.log(gatewayInfoMessageStr);
      console.log('=====================\n');

      ws.send(gatewayInfoMessageStr);
    }, 30000);
  });

  ws.on('message', async (data) => {
    try {
      // 改進的消息類型檢測 - 支援大量小分片
      let isJsonMessage = false;

      if (typeof data === 'string') {
        isJsonMessage = true;
      } else if (data instanceof Buffer) {
        // 更嚴格的 JSON 檢測邏輯
        if (data.length > 0 && data[0] === 0x7B) { // 以 '{' 開頭
          // 進一步檢查是否真的是 JSON
          if (data.length > 1 && data[data.length - 1] === 0x7D) { // 以 '}' 結尾
            isJsonMessage = true;
          } else if (data.length < 500) { // 小數據包，嘗試解析
            try {
              const str = data.toString('utf8');
              if (str.trim().startsWith('{') && str.trim().endsWith('}')) {
                JSON.parse(str); // 嘗試解析以確認是有效 JSON
                isJsonMessage = true;
              }
            } catch (e) {
              // 解析失敗，不是 JSON
              isJsonMessage = false;
            }
          }
        }
        // 對於分片數據，如果有活躍的分片接收器且數據長度符合預期，直接當作二進制處理
        else if (chunkReceiver && data.length >= 4) {
          // 這很可能是嵌入式 Index 的分片數據
          isJsonMessage = false;
        }
      }

      if (isJsonMessage) {
        // JSON 訊息處理
        const message = JSON.parse(data.toString());

        console.log('\n===== 收到消息 =====');
        console.log('原始數據長度:', data.length, '字節');
        console.log('消息類型:', message.type || '未知類型');

        // 格式化顯示消息內容
        console.log('消息內容:');
        console.log(JSON.stringify(message, null, 2));

      // 特別處理歡迎消息
      if (message.type === 'welcome') {
        console.log('說明: 收到歡迎消息，連接成功建立');

        // 收到welcome消息後發送網關信息，包含分片能力
        const gatewayInfoMessage = {
          type: 'gatewayInfo',
          info: {
            macAddress: wsInfo.macAddress || 'AA:BB:CC:DD:EE:FF',
            model: wsInfo.model || 'Gateway Model 003',
            hardwareVersion: wsInfo.hardwareVersion || '*******',
            wifiFirmwareVersion: wsInfo.wifiFirmwareVersion || '1.0.0',
            btFirmwareVersion: wsInfo.btFirmwareVersion || '2.0.0',
            ipAddress: wsInfo.ipAddress || '*************',

            // 新增：分片傳輸能力支援
            chunkingSupport: {
              enabled: true,                 // 是否支援分片傳輸
              maxChunkSize: maxChunkSize,     // 支援硬體限制的分片大小
              maxSingleMessageSize: maxSingleMessageSize,     // 單次發送訊息的最大數據量限制（bytes）
              embeddedIndex: true,           // 是否支援嵌入式 Index 模式
              jsonHeader: true,              // 是否支援 JSON Header 模式（向後兼容）
              supportedFormat: wsInfo.preferredFormat || 'rawdata'  // 新增：偏好的格式
            }
          }
        };

        const gatewayInfoMessageStr = JSON.stringify(gatewayInfoMessage);
        console.log('\n===== 發送消息 =====');
        console.log('類型: 網關信息消息 (收到welcome後發送)');
        console.log('內容:');
        console.log(JSON.stringify(gatewayInfoMessage, null, 2));
        console.log('JSON格式:');
        console.log(gatewayInfoMessageStr);
        console.log('=====================\n');

        ws.send(gatewayInfoMessageStr);
      }

      // 特別處理確認消息
      if (message.type === 'pong') {
        console.log('說明: 收到服務器 pong 回應');
      }

      // 處理分片傳輸相關訊息
      if (message.type === 'image_chunk_start') {
        console.log('🚀 開始接收分片傳輸:', message);
        chunkReceiver = new ChunkReceiver(ws);
        // 保持全局失敗模式設定
        chunkReceiver.globalSimulateFailure = globalSimulateFailure;
        await chunkReceiver.handleChunkStart(message);
        return;
      }

      if (message.type === 'image_chunk_complete') {
        console.log('🏁 分片傳輸完成:', message);
        if (chunkReceiver) {
          await chunkReceiver.handleChunkComplete(message);
          chunkReceiver = null;
        }
        return;
      }

      // 特別處理網關信息確認消息
      if (message.type === 'gatewayInfoAck') {
        if (message.success === false && message.fatal === true) {
          console.error('嚴重錯誤: 服務器因安全問題強制中斷連線');
          console.error('錯誤原因:', message.message);
          console.error('連線將被終止，請檢查網關配置');
        } else if (message.success === false) {
          console.warn('網關信息更新失敗:', message.message);
        } else {
          console.log('網關信息更新成功');
        }
      }

      // 處理圖像更新消息
      if (message.type === 'update_preview') {
        console.log('說明: 收到圖像更新消息');

        const deviceMac = message.deviceMac;
        const imageCode = message.imageCode;
        const imageData = message.imageData;

        if (deviceMac && imageCode) {
          // 更新本地存儲的 imageCode
          deviceImageCodes[deviceMac] = imageCode;
          console.log(`已更新設備 ${deviceMac} 的本地 imageCode 為: ${imageCode}`);

          // 保存圖像數據（如果有）
          if (imageData && typeof imageData === 'string') {
            const imageDataLength = imageData.length;
            console.log(`圖像數據長度: ${imageDataLength} 字符`);

            if (imageData.startsWith('data:image')) {
              console.log('圖像格式: Data URL (base64)');
              console.log('圖像數據將保存到 saved_images 目錄');
              saveBase64Image(imageData, deviceMac);
            } else {
              console.log('圖像格式: 未知格式，無法處理');
            }
          }
        }
      }

      // 處理圖像數據（向後兼容）
      if (message.hasOwnProperty('imageData') && message.type !== 'update_preview') {
        console.log('說明: 檢測到消息中包含 imageData 欄位');

        // 檢查 imageData 格式
        if (typeof message.imageData === 'string') {
          const imageDataLength = message.imageData.length;
          console.log(`圖像數據長度: ${imageDataLength} 字符`);

          if (message.imageData.startsWith('data:image')) {
            console.log('圖像格式: Data URL (base64)');
            console.log('圖像數據將保存到 saved_images 目錄');
            saveBase64Image(message.imageData, message.deviceMac || 'unknown', message.imageCode);
          } else {
            console.log('圖像格式: 未知格式，無法處理');
          }
        } else {
          console.log('圖像數據類型不是字符串，無法處理');
        }
      }

      // 儲存 imageCode (無論是否有圖像數據)
      if (message.deviceMac && message.imageCode) {
        console.log(`收到裝置 ${message.deviceMac} 的 imageCode: ${message.imageCode}`);
        deviceImageCodes[message.deviceMac] = message.imageCode;
        console.log(`已將 imageCode 儲存至本地變數，將在下次發送裝置狀態時使用`);
      }

      // 處理圖像數據（向後兼容）
      if (message.hasOwnProperty('imageData') && message.type !== 'update_preview') {
        console.log('說明: 檢測到消息中包含 imageData 欄位');

        // 檢查 imageData 格式
        if (typeof message.imageData === 'string') {
          const imageDataLength = message.imageData.length;
          console.log(`圖像數據長度: ${imageDataLength} 字符`);

          if (message.imageData.startsWith('data:image')) {
            console.log('圖像格式: Data URL (base64)');
            console.log('圖像數據將保存到 saved_images 目錄');
            saveBase64Image(message.imageData, message.deviceMac || 'unknown', message.imageCode);
          } else {
            console.log('圖像格式: 未知格式，無法處理');
          }
        } else {
          console.log('圖像數據類型不是字符串，無法處理');
        }
      }

      // 處理原始數據 (rawdata)
      if (message.hasOwnProperty('rawdata')) {
        console.log('說明: 檢測到消息中包含 rawdata 欄位');

        // 檢查是否包含 dataType 欄位
        if (message.hasOwnProperty('dataType')) {
          console.log('Rawdata 格式:', message.dataType);
        }

        try {
          const dataType = message.dataType || 'rawdata';
          saveRawData(message.rawdata, message.deviceMac || 'unknown', message.imageCode, dataType);
        } catch (err) {
          console.error('處理 rawdata 時出錯:', err.message);
        }
      }

        console.log('=====================\n');
      } else {
        // 二進制數據處理（嵌入式Index分片）
        if (chunkReceiver) {
          await chunkReceiver.handleBinaryChunkData(data);
          return;
        } else {
          console.warn('收到二進制數據但沒有活躍的分片接收器');
          return;
        }
      }
    } catch (error) {
      // 改進的錯誤處理 - 支援大量小分片場景
      const isLikelyChunkData = chunkReceiver && data instanceof Buffer && data.length >= 4;

      if (isLikelyChunkData) {
        // 如果有活躍的分片接收器且數據看起來像分片，直接處理
        console.log(`嘗試將數據作為分片處理 (長度: ${data.length} bytes)`);
        try {
          await chunkReceiver.handleBinaryChunkData(data);
          return; // 成功處理，直接返回
        } catch (chunkError) {
          console.error('作為分片數據處理失敗:', chunkError.message);
        }
      }

      // 只有在非分片數據或分片處理失敗時才記錄詳細錯誤
      console.error('\n===== 收到消息處理錯誤 =====');
      console.error('錯誤:', error.message);

      // 簡化調試信息，避免在大量分片時產生過多日誌
      if (data instanceof Buffer) {
        console.error(`數據類型: Buffer, 長度: ${data.length} bytes`);

        // 只對小數據包顯示詳細信息
        if (data.length <= 100) {
          console.error(`前16字節 (hex): ${data.subarray(0, Math.min(16, data.length)).toString('hex')}`);
          console.error(`完整數據 (string): ${data.toString('utf8')}`);
        } else {
          console.error(`前16字節 (hex): ${data.subarray(0, 16).toString('hex')}`);
        }
      } else {
        console.error(`數據類型: ${typeof data}, 內容: ${data}`);
      }
      console.error('================================\n');
    }
  });

  // 檢查 base64 字符串是否有效
  function isValidBase64(base64Str) {
    const base64Regex = /^[A-Za-z0-9+/]+={0,2}$/;
    return base64Regex.test(base64Str) && base64Str.length % 4 === 0;
  }

  // 保存 Base64 編碼的圖像
  function saveBase64Image(base64Data, deviceMac, imageCode = null) {
    try {
      console.log(`準備處理 imageData 欄位的數據並保存為圖片，裝置 MAC: ${deviceMac}`);

      // 如果有 imageCode，記錄關聯
      if (imageCode) {
        console.log(`關聯的 imageCode: ${imageCode}`);
      }

      // 從 Data URL 中提取 base64 數據部分
      const matches = base64Data.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);

      if (!matches || matches.length !== 3) {
        console.log('無效的 data URL 格式');
        return;
      }

      // 提取 MIME 類型和 base64 數據
      const mimeType = matches[1];
      const base64Buffer = matches[2];
      const extension = mimeType.split('/')[1] || 'png';

      // 檢查提取出的 base64 字符串是否有效
      if (!isValidBase64(base64Buffer.replace(/\s/g, ''))) {
        console.warn('從 Data URL 提取的 base64 字符串無效，跳過保存');
        return;
      }

      // 創建圖像數據的 buffer
      const imageBuffer = Buffer.from(base64Buffer, 'base64');

      // 建立保存圖像的目錄
      const saveDir = path.join(__dirname, 'saved_images');
      if (!fs.existsSync(saveDir)) {
        fs.mkdirSync(saveDir, { recursive: true });
      }

      // 創建文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `preview_${deviceMac.replace(/:/g, '')}_${timestamp}.${extension}`;
      const filePath = path.join(saveDir, fileName);

      // 寫入文件
      fs.writeFileSync(filePath, imageBuffer);

      console.log(`已成功將圖像保存到: ${filePath}`);
    } catch (err) {
      console.error('保存圖像文件時出錯:', err.message);
    }
  }

  // 保存原始數據為 bin 檔案
  function saveRawData(rawdata, deviceMac = 'unknown', imageCode = null, format = 'rawdata') {
    try {
      console.log(`準備保存 ${format} 格式的數據為 bin 檔案，裝置 MAC: ${deviceMac}`);

      let rawBuffer;

      // 處理不同格式的 rawdata
      if (typeof rawdata === 'string') {
        // 如果是 base64 字符串，先解碼
        if (isValidBase64(rawdata.replace(/\s/g, ''))) {
          console.log('處理 base64 編碼的原始數據');
          rawBuffer = Buffer.from(rawdata, 'base64');
        } else {
          console.warn('rawdata 不是有效的 base64 字符串');
          return;
        }
      } else if (Buffer.isBuffer(rawdata)) {
        // 如果已經是 Buffer，直接使用
        rawBuffer = rawdata;
      } else if (Array.isArray(rawdata)) {
        // 如果是數組，轉換為 Buffer
        rawBuffer = Buffer.from(rawdata);
      } else {
        console.error('不支援的 rawdata 格式:', typeof rawdata);
        return;
      }

      // 檢查 buffer 是否有效
      if (!Buffer.isBuffer(rawBuffer) || rawBuffer.length === 0) {
        console.warn('無效的原始數據 buffer，跳過保存');
        return;
      }

      // 建立保存原始數據的目錄（與圖像保存在同一位置）
      const saveDir = path.join(__dirname, 'saved_images');
      if (!fs.existsSync(saveDir)) {
        fs.mkdirSync(saveDir, { recursive: true });
      }

      // 根據格式調整檔案名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `${format}_${deviceMac.replace(/:/g, '')}_${timestamp}.bin`;
      const filePath = path.join(saveDir, fileName);

      // 寫入文件
      fs.writeFileSync(filePath, rawBuffer);

      console.log(`已成功將 ${format} 數據保存到: ${filePath}`);
      console.log(`數據大小: ${rawBuffer.length} 字節`);

      // 如果是壓縮格式，嘗試解壓縮驗證
      if (format !== 'rawdata') {
        try {
          const decompressed = decompressRawdata(rawBuffer, format);
          console.log(`解壓縮驗證成功，解壓縮後大小: ${decompressed.length} 字節`);

          if (rawBuffer.length < decompressed.length) {
            const compressionRatio = (rawBuffer.length / decompressed.length * 100).toFixed(1);
            console.log(`壓縮比: ${compressionRatio}%`);
          }

          // 保存解壓縮後的數據
          const decompressedFileName = `rawdata_decompressed_${deviceMac.replace(/:/g, '')}_${timestamp}.bin`;
          const decompressedFilePath = path.join(saveDir, decompressedFileName);
          fs.writeFileSync(decompressedFilePath, Buffer.from(decompressed));
          console.log(`解壓縮數據已保存到: ${decompressedFilePath}`);
        } catch (decompressError) {
          console.error('解壓縮驗證失敗:', decompressError.message);
        }
      }

      // 如果提供了 imageCode，記錄關聯
      if (imageCode) {
        console.log(`關聯的 imageCode: ${imageCode}`);
      }

      // 顯示原始數據的前幾個字節（用於調試）
      const previewBytes = rawBuffer.subarray(0, Math.min(16, rawBuffer.length));
      console.log(`數據前 ${previewBytes.length} 字節 (hex): ${Buffer.from(previewBytes).toString('hex')}`);

    } catch (err) {
      console.error('保存原始數據時出錯:', err.message);
    }
  }

  ws.on('error', (error) => {
    console.error('WebSocket 錯誤:', error);
    clearIntervals();
  });

  ws.on('close', (code, reason) => {
    console.log(`連接已關閉，代碼: ${code}，原因: ${reason}`);
    clearIntervals();

    // 詢問用戶是否要重連
    promptReconnect(wsInfo, maxChunkSize, maxSingleMessageSize);
  });

  // 處理程序結束時，關閉 WebSocket 連接
  process.on('SIGINT', () => {
    console.log('關閉 WebSocket 連接...');
    clearIntervals();
    ws.close();
    rl.close();
    process.exit();
  });

  return ws;
}

// 提示用戶是否重新連接
async function promptReconnect(wsInfo, maxChunkSize = 20, maxSingleMessageSize = 2048) {
  const answer = await prompt('連接已斷開，是否嘗試重新連接? (y/n): ');
  if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
    console.log('嘗試重新連接...');
    connectWebSocket(wsInfo, maxChunkSize, maxSingleMessageSize);
  } else {
    console.log('不再嘗試重新連接。程序將退出。');
    rl.close();
    process.exit();
  }
}

// 生成隨機MAC地址
function generateRandomMac() {
  const hexDigits = "0123456789ABCDEF";
  let mac = "";
  for (let i = 0; i < 6; i++) {
    let part = "";
    for (let j = 0; j < 2; j++) {
      part += hexDigits.charAt(Math.floor(Math.random() * 16));
    }
    mac += part;
    if (i < 5) mac += ":";
  }
  return mac;
}

// 主函數
async function main() {
  try {
    console.log('EPD 網關模擬器（從複製的WebSocket資訊啟動）');

    // 詢問使用者要模擬的硬體 chunk size
    console.log('\n===== 硬體分片能力設定 =====');
    console.log('請設定要模擬的硬體最大分片大小：');
    console.log('1. 20 bytes (預設，模擬硬體限制)');
    console.log('2. 200 bytes (較大分片)');
    console.log('3. 1024 bytes (標準分片)');
    console.log('4. 4096 bytes (大分片)');
    console.log('5. 自定義大小');

    const chunkSizeChoice = await prompt('請選擇分片大小選項 (1-5，預設為1): ') || '1';
    let maxChunkSize = 20; // 預設值

    switch (chunkSizeChoice) {
      case '1':
        maxChunkSize = 20;
        break;
      case '2':
        maxChunkSize = 200;
        break;
      case '3':
        maxChunkSize = 1024;
        break;
      case '4':
        maxChunkSize = 4096;
        break;
      case '5':
        const customSize = await prompt('請輸入自定義分片大小 (bytes): ');
        maxChunkSize = parseInt(customSize) || 20;
        break;
      default:
        maxChunkSize = 20;
    }

    console.log(`已設定最大分片大小為: ${maxChunkSize} bytes`);
    console.log('===============================\n');

    // 新增：詢問 maxSingleMessageSize 設定
    console.log('\n===== JSON 訊息大小限制設定 =====');
    console.log('請設定單次發送 JSON 訊息的最大大小限制：');
    console.log('1. 500 bytes (小值，用於測試 JSON 訊息大小檢查)');
    console.log('2. 1024 bytes (中等值)');
    console.log('3. 2048 bytes (標準值，預設)');
    console.log('4. 4096 bytes (大值)');
    console.log('5. 自定義大小');

    const messageSizeChoice = await prompt('請選擇 JSON 訊息大小限制選項 (1-5，預設為3): ') || '3';
    let maxSingleMessageSize = 2048; // 預設值

    switch (messageSizeChoice) {
      case '1':
        maxSingleMessageSize = 500;
        break;
      case '2':
        maxSingleMessageSize = 1024;
        break;
      case '3':
        maxSingleMessageSize = 2048;
        break;
      case '4':
        maxSingleMessageSize = 4096;
        break;
      case '5':
        const customMessageSize = await prompt('請輸入自定義 JSON 訊息大小限制 (bytes): ');
        maxSingleMessageSize = parseInt(customMessageSize) || 2048;
        break;
      default:
        maxSingleMessageSize = 2048;
    }

    console.log(`已設定 JSON 訊息大小限制為: ${maxSingleMessageSize} bytes`);
    console.log('===============================\n');

    // 新增：詢問偏好的 rawdata 格式
    console.log('\n===== Rawdata 格式偏好設定 =====');
    console.log('請選擇要模擬的偏好 rawdata 格式：');
    console.log('1. rawdata (預設，未壓縮格式)');
    console.log('2. runlendata (Run-Length 壓縮格式)');

    const formatChoice = await prompt('請選擇格式偏好選項 (1-2，預設為1): ') || '1';
    let preferredFormat = 'rawdata';

    switch (formatChoice) {
      case '1':
        preferredFormat = 'rawdata';
        break;
      case '2':
        preferredFormat = 'runlendata';
        break;
      default:
        preferredFormat = 'rawdata';
    }

    console.log(`已設定偏好格式: ${preferredFormat}`);
    console.log('===============================\n');

    // 生成並顯示模擬MAC地址和網關信息
    const defaultMac = generateRandomMac();
    const defaultDeviceMac = generateRandomMac();
    const randomIp = `192.168.${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}`;

    console.log('\n===== 模擬設備資訊 =====');
    console.log(`網關 MAC 地址: ${defaultMac}`);
    console.log(`網關 IP 地址: ${randomIp}`);
    console.log(`網關型號: Gateway Model XYZ`);
    console.log(`WiFi固件版本: 1.0.0`);
    console.log(`藍芽固件版本: 2.0.0`);
    console.log(`預設設備 MAC 地址: ${defaultDeviceMac}`);
    console.log(`硬體分片能力: 最大 ${maxChunkSize} bytes`);
    console.log('請先在server端新增這些設備，然後再繼續\n');
    console.log('===========================\n');

    console.log('請從網關管理頁面複製WebSocket登入資訊（點擊鑰匙圖標按鈕）');

    const wsInfoStr = await prompt('\n請貼上WebSocket登入資訊 (JSON格式): ');

    let wsInfo;
    try {
      wsInfo = JSON.parse(wsInfoStr);
    } catch (error) {
      throw new Error('無法解析WebSocket登入資訊，請確保格式正確的JSON');
    }

    // 驗證必要的字段
    if (!wsInfo.url || !wsInfo.token) {
      throw new Error('WebSocket登入資訊缺少必要的字段 (url, token)');
    }

    // 添加網關信息到wsInfo，使用一開始生成的 MAC 地址
    wsInfo.macAddress = defaultMac;
    wsInfo.defaultDeviceMac = defaultDeviceMac;
    wsInfo.ipAddress = randomIp;
    wsInfo.model = 'Gateway Model 003';
    wsInfo.wifiFirmwareVersion = '1.0.0';
    wsInfo.btFirmwareVersion = '2.0.0';
    wsInfo.name = `Gateway-${defaultMac.substring(9).replace(/:/g, '')}`;
    wsInfo.preferredFormat = preferredFormat; // 添加格式偏好

    console.log('\n已解析WebSocket登入資訊:');
    console.log(`URL: ${wsInfo.url}`);
    console.log(`Token: ${wsInfo.token.substring(0, 10)}...`);
    console.log(`Protocol: ${wsInfo.protocol || 'json'}`);
    console.log(`網關 MAC: ${wsInfo.macAddress}`);
    console.log(`網關名稱: ${wsInfo.name}`);
    console.log(`網關 IP: ${wsInfo.ipAddress}`);
    console.log(`網關型號: ${wsInfo.model}`);
    console.log(`WiFi固件版本: ${wsInfo.wifiFirmwareVersion}`);
    console.log(`藍芽固件版本: ${wsInfo.btFirmwareVersion}`);
    console.log(`預設設備 MAC: ${wsInfo.defaultDeviceMac}`);

    // 顯示原始WebSocket登入資訊（單行格式）
    console.log('\n原始WebSocket登入資訊 (可複製):');
    console.log(JSON.stringify(wsInfo));

    // 連接到 WebSocket 服務器
    console.log('\n正在連接到 WebSocket 服務器...');
    connectWebSocket(wsInfo, maxChunkSize, maxSingleMessageSize);

    console.log('\n使用說明:');
    console.log('1. 此測試客戶端會自動嘗試連接到WebSocket服務器');
    console.log('2. 會自動發送 ping、設備狀態和網關信息');
    console.log('3. 模擬三種不同型號的設備:');
    console.log('   - 2.9吋 BWR 設備');
    console.log('   - 6吋 BW 設備');
    console.log('   - 3.7吋 BWRY 設備');
    console.log('   - 每個設備使用固定的隨機MAC地址（連接時生成）');
    console.log('4. 設備回報格式符合最新 server 架構要求:');
    console.log('   - 設備回報不包含 dataId（由前端或API控制的欄位）');
    console.log('   - 設備回報不包含 imageCode（避免覆蓋資料庫值）');
    console.log('   - 當收到 server 圖像更新時，會自動更新本地 imageCode');
    console.log('   - 下次設備狀態回報時會包含更新後的 imageCode');
    console.log('5. 分片傳輸功能:');
    console.log(`   - 支援硬體限制的分片大小: ${maxChunkSize} bytes`);
    console.log('   - 支援嵌入式Index模式的分片接收');
    console.log('   - 自動重組分片數據並保存為完整檔案');
    console.log('   - 提供詳細的分片接收進度和統計信息');
    console.log('6. 使用以下命令管理模擬設備:');
    console.log('   - help: 顯示命令幫助');
    console.log('   - add: 新增自定義設備');
    console.log('   - list: 列出所有模擬設備');
    console.log('   - remove <序號>: 移除指定序號的自定義設備');
    console.log('   - request-image <裝置MAC>: 請求特定裝置的預覽圖像');
    console.log('   - test-chunk-fail: 啟用全局分片失敗模式（所有設備）');
    console.log('   - reset-chunk-fail: 重置分片失敗模式為正常');
    console.log('   - q: 退出程序');
    console.log('7. 如果連接斷開，系統會詢問您是否重連');
    console.log('8. 按 Ctrl+C 也可以終止程序');
    console.log('9. 收到的數據會自動保存到 saved_images 目錄:');
    console.log('   - 圖片數據 (imageData) 保存為 .png/.jpg 等圖片格式');
    console.log('   - 原始數據 (rawdata) 保存為 .bin 二進制檔案');
    console.log('   - 分片重組數據保存為 rawdata_chunked_*.bin 檔案');
    console.log('10. maxSingleMessageSize 功能測試:');
    console.log(`   - 當前設定 maxSingleMessageSize 為 ${maxSingleMessageSize} bytes`);
    console.log('   - 使用 test-chunk-fail 命令啟用全局分片失敗模式');
    console.log('   - 啟用後，所有設備的分片傳輸都會模擬失敗');
    console.log('   - 客戶端會接收一半數量的分片，然後一直回報中間值 ACK');
    console.log('   - 這會讓 server 檢測到進度停滯，觸發失敗處理');
    console.log('   - server 會回退到 update_preview 機制');
    console.log('   - 如果 update_preview JSON 過大會被拒絕並記錄事件');
    console.log('   - 使用 reset-chunk-fail 命令可以恢復正常模式');
    console.log('   - 這可以驗證 maxSingleMessageSize 功能是否正常工作\n');

  } catch (error) {
    console.error('錯誤:', error.message);
    rl.close();
    process.exit(1);
  }
}

// 啟動程序
main().catch(error => {
  console.error('未處理的錯誤:', error);
  rl.close();
  process.exit(1);
});
