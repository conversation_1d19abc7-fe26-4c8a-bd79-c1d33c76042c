// src/utils/api/softwareApi.ts
import { buildEndpointUrl } from './apiConfig';
import { useAuthStore } from '../../store/authStore';

export interface Software {
  _id: string;
  name: string;
  description: string;
  version: string;
  deviceType: 'gateway' | 'epd';
  model: string;
  functionType: 'wifi' | 'ble';
  minHwVersion: string;
  maxHwVersion: string;
  originalFilename: string;
  fileSize: number;
  binSize: number;
  checksum: string;
  binFileId: string;
  extractedBinId: string;
  status: 'active' | 'disabled' | 'deprecated';
  isEnabled: boolean;
  compatibility: Compatibility;
  uploadedBy: string;
  uploadDate: string;
  lastModified: string;
  modifiedBy: string;
  tags: string[];
  category: string;
  createdAt: string;
  updatedAt: string;
}



export interface Compatibility {
  minHardwareVersion: string;
  maxHardwareVersion: string;
  supportedSizes: string[];
  requirements: string[];
}

export interface SoftwareListResponse {
  software: Software[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface SoftwareFilters {
  deviceType?: string;
  functionType?: string;
  status?: string;
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface SoftwareUploadData {
  name: string;
  description?: string;
  tags?: string[];
}

export interface SoftwareStatistics {
  totalSoftware: number;
  enabledSoftware: number;
  disabledSoftware: number;
  totalSize: number;
}

export interface ValidationReport {
  filename: string;
  status: 'valid' | 'error' | 'unsupported';
  message: string;
  details?: {
    deviceType: string;
    model: string;
    functionType: string;
    version: string;
    minHwVersion: string;
    maxHwVersion: string;
    totalSize: number;
    binSize: number;
    checksum: string;
    compressionRatio: string;
  };
}

/**
 * 獲取軟體列表
 */
export async function getSoftwareList(filters: SoftwareFilters = {}): Promise<SoftwareListResponse> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    const response = await fetch(`${buildEndpointUrl('software')}?${params}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      const error = await response.json();
      throw new Error(error.error?.message || '獲取軟體列表失敗');
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('獲取軟體列表失敗:', error);
    throw error;
  }
}

/**
 * 獲取軟體詳細資訊
 */
export async function getSoftwareById(id: string): Promise<Software> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const response = await fetch(buildEndpointUrl('software', id), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      const error = await response.json();
      throw new Error(error.error?.message || '獲取軟體詳細資訊失敗');
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error(`獲取軟體詳細資訊失敗 (ID: ${id}):`, error);
    throw error;
  }
}

/**
 * 上傳軟體
 */
export async function uploadSoftware(file: File, data: SoftwareUploadData): Promise<{ id: string; message: string }> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const formData = new FormData();
    formData.append('file', file);
    formData.append('name', data.name);

    if (data.description) {
      formData.append('description', data.description);
    }



    if (data.tags && data.tags.length > 0) {
      formData.append('tags', JSON.stringify(data.tags));
    }

    const response = await fetch(buildEndpointUrl('software', 'upload'), {
      method: 'POST',
      headers: {
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: formData,
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      const error = await response.json();
      throw new Error(error.error?.message || '軟體上傳失敗');
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('軟體上傳失敗:', error);
    throw error;
  }
}

/**
 * 更新軟體基本資訊
 */
export async function updateSoftware(
  id: string,
  updateData: { name?: string; description?: string }
): Promise<void> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const response = await fetch(buildEndpointUrl('software', id), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify(updateData),
      credentials: 'include',
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error?.message || '更新軟體失敗');
    }

    if (!result.success) {
      throw new Error(result.error?.message || '更新軟體失敗');
    }
  } catch (error) {
    console.error('更新軟體錯誤:', error);
    throw error;
  }
}

/**
 * 更新軟體狀態
 */
export async function updateSoftwareStatus(
  id: string,
  statusData: { isEnabled?: boolean; status?: string }
): Promise<void> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const response = await fetch(buildEndpointUrl('software', `${id}/status`), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify(statusData),
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      const error = await response.json();
      throw new Error(error.error?.message || '更新軟體狀態失敗');
    }
  } catch (error) {
    console.error(`更新軟體狀態失敗 (ID: ${id}):`, error);
    throw error;
  }
}

/**
 * 下載軟體檔案
 */
export async function downloadSoftware(id: string, type: 'original' | 'pure' = 'original'): Promise<void> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const response = await fetch(`${buildEndpointUrl('software', `${id}/download`)}?type=${type}`, {
      method: 'GET',
      headers: {
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      const error = await response.json();
      throw new Error(error.error?.message || '下載軟體失敗');
    }

    // 獲取檔案名稱
    const contentDisposition = response.headers.get('Content-Disposition');
    let filename = 'software.bin';

    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
      if (filenameMatch && filenameMatch[1]) {
        filename = decodeURIComponent(filenameMatch[1].replace(/['"]/g, ''));
      }
    }

    // 創建下載連結
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  } catch (error) {
    console.error(`下載軟體失敗 (ID: ${id}):`, error);
    throw error;
  }
}

/**
 * 刪除軟體
 */
export async function deleteSoftware(id: string): Promise<void> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const response = await fetch(buildEndpointUrl('software', id), {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      const error = await response.json();
      throw new Error(error.error?.message || '刪除軟體失敗');
    }
  } catch (error) {
    console.error(`刪除軟體失敗 (ID: ${id}):`, error);
    throw error;
  }
}

/**
 * 獲取啟用的軟體列表（供其他頁面選單使用）
 */
export async function getEnabledSoftware(filters: { deviceType?: string; functionType?: string } = {}): Promise<Software[]> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== 'all') {
        params.append(key, value);
      }
    });

    const response = await fetch(`${buildEndpointUrl('software', 'enabled')}?${params}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      const error = await response.json();
      throw new Error(error.error?.message || '獲取啟用軟體列表失敗');
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('獲取啟用軟體列表失敗:', error);
    throw error;
  }
}

/**
 * 獲取軟體統計資訊
 */
export async function getSoftwareStatistics(): Promise<SoftwareStatistics> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const response = await fetch(buildEndpointUrl('software', 'statistics'), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      const error = await response.json();
      throw new Error(error.error?.message || '獲取軟體統計資訊失敗');
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('獲取軟體統計資訊失敗:', error);
    throw error;
  }
}

/**
 * 檢查軟體名稱是否已存在
 */
export async function checkSoftwareNameExists(name: string): Promise<boolean> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const response = await fetch(buildEndpointUrl('software', `check-name/${encodeURIComponent(name)}`), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      const error = await response.json();
      throw new Error(error.error?.message || '檢查軟體名稱失敗');
    }

    const result = await response.json();
    return result.data.exists;
  } catch (error) {
    console.error('檢查軟體名稱失敗:', error);
    throw error;
  }
}

/**
 * 檢查軟體版本+設備類型+功能類型組合是否已存在
 */
export async function checkVersionDeviceFunctionExists(version: string, deviceType: string, functionType: string): Promise<boolean> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const response = await fetch(buildEndpointUrl('software', 'check-version-device-function'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify({
        version,
        deviceType,
        functionType
      }),
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      const error = await response.json();
      throw new Error(error.error?.message || '檢查軟體版本組合失敗');
    }

    const result = await response.json();
    return result.data.exists;
  } catch (error) {
    console.error('檢查軟體版本組合失敗:', error);
    throw error;
  }
}

/**
 * 驗證bin檔案
 */
export async function validateBinFile(file: File): Promise<ValidationReport> {
  try {
    // 獲取認證 token
    const { token } = useAuthStore.getState();

    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(buildEndpointUrl('software', 'validate'), {
      method: 'POST',
      headers: {
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: formData,
      credentials: 'include', // 包含 cookie
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('未登入或登入已過期');
      }
      const error = await response.json();
      throw new Error(error.error?.message || '驗證bin檔案失敗');
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error('驗證bin檔案失敗:', error);
    throw error;
  }
}

/**
 * 格式化檔案大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化日期
 */
export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}
