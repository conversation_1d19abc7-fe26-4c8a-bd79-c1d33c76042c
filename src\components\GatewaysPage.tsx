import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Search, RefreshCw, Plus, Trash2, Edit, AlertCircle, Wifi, Bluetooth, Grid, Trash, MoreHorizontal, Power, Key, CheckSquare } from 'lucide-react';
import { Gateway, GatewayStatus } from '../types/gateway';
import { Store } from '../types/store';
import { getAllGateways, deleteGateway, syncGateways, restartGateway, getGateway, getGatewayStats } from '../utils/api/gatewayApi';
import { GatewayStatusBadge } from './ui/GatewayStatusBadge';
import { AddGatewayModal } from './AddGatewayModal';
import { EditGatewayModal } from './EditGatewayModal';
import { UpgradeWifiFirmwareModal } from './UpgradeWifiFirmwareModal';
import { UpgradeBtFirmwareModal } from './UpgradeBtFirmwareModal';
import { showSuccessNotification, showErrorNotification, showWarningNotification } from '../utils/bubbleNotification';
import { subscribeToGatewayStatus, subscribeToGatewayCRUD, subscribeToGatewayStatsUpdate, GatewayStatusEvent, GatewayStatsUpdateEvent } from '../utils/websocketClient';

interface GatewaysPageProps {
  store: Store;
}

export function GatewaysPage({ store }: GatewaysPageProps) {
  const { t } = useTranslation();
  const [gateways, setGateways] = useState<Gateway[]>([]);
  const [filteredGateways, setFilteredGateways] = useState<Gateway[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<GatewayStatus | ''>('');
  const [modelFilter, setModelFilter] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showWifiUpgradeModal, setShowWifiUpgradeModal] = useState(false);
  const [showBtUpgradeModal, setShowBtUpgradeModal] = useState(false);
  const [selectedGateway, setSelectedGateway] = useState<Gateway | null>(null);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [syncingGateways, setSyncingGateways] = useState(false);
  const [showMoreMenu, setShowMoreMenu] = useState(false);
  const moreMenuRef = useRef<HTMLDivElement>(null);
  const [visibleFields, setVisibleFields] = useState<Record<string, boolean>>({
    macAddress: true,
    status: true,
    lastSeen: true,
    model: true,
    hardwareVersion: true,
    wifiFirmwareVersion: true,
    btFirmwareVersion: true,
    ipAddress: true,
  });
  const [showFieldManager, setShowFieldManager] = useState(false);
  const fieldManagerRef = useRef<HTMLDivElement>(null);
  const fieldManagerButtonRef = useRef<HTMLButtonElement>(null);

  // 即時更新相關狀態
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true);
  const [realTimeConnectionStatus, setRealTimeConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('disconnected');

  // 網關統計信息狀態
  const [gatewayStats, setGatewayStats] = useState<{
    totalConnected: number;
    busyGateways: number;
    idleGateways: number;
    busyGatewayDetails: Array<{
      id: string;
      name: string;
      storeId: string;
      ipAddress: string;
      activeTransmissions: any;
    }>;
    idleGatewayDetails: Array<{
      id: string;
      name: string;
      storeId: string;
      ipAddress: string;
    }>;
    summary: {
      totalConnected: number;
      busyCount: number;
      idleCount: number;
      busyPercentage: number;
      idlePercentage: number;
    };
  } | null>(null);
  const [statsLoading, setStatsLoading] = useState(false);

  // 獲取網關統計信息
  const fetchGatewayStats = async () => {
    try {
      setStatsLoading(true);
      const stats = await getGatewayStats(store.id);
      setGatewayStats(stats);
    } catch (err: any) {
      console.error('獲取網關統計信息失敗:', err);
    } finally {
      setStatsLoading(false);
    }
  };

  // 獲取網關列表
  const fetchGateways = async () => {
    try {
      setLoading(true);
      setError(null);

      // 使用門店ID獲取網關列表
      const gatewayList = await getAllGateways(store.id);

      // 將日期字符串轉換為 Date 對象
      const formattedGateways = gatewayList.map(gateway => ({
        ...gateway,
        lastSeen: gateway.lastSeen ? new Date(gateway.lastSeen) : null,
        createdAt: gateway.createdAt ? new Date(gateway.createdAt) : null,
        updatedAt: gateway.updatedAt ? new Date(gateway.updatedAt) : null,
      }));

      setGateways(formattedGateways);
      applyFiltersWithPaginationPreservation(formattedGateways, searchTerm, statusFilter, modelFilter);

      // 同時獲取統計信息
      fetchGatewayStats();
    } catch (err: any) {
      console.error('獲取網關列表失敗:', err);
      setError(err.message || '獲取網關列表失敗');
    } finally {
      setLoading(false);
    }
  };

  // 初始加載和門店變化時重新獲取網關列表
  useEffect(() => {
    if (store?.id) {
      console.log(`門店變更為 ${store.id}，重新獲取網關列表`);
      fetchGateways();
    }
  }, [store?.id]);

  // 網關狀態即時更新Hook
  useEffect(() => {
    if (!store?.id || !isRealTimeEnabled) return;

    console.log(`啟用網關狀態即時更新: storeId=${store.id}`);
    setRealTimeConnectionStatus('connecting');

    // 處理網關狀態更新事件（保持選取狀態）
    const handleGatewayStatusUpdate = (event: GatewayStatusEvent) => {
      if (event.storeId !== store.id) return;

      console.log(`收到網關狀態更新: ${event.gateways.length} 個網關`);

      // 選擇性更新網關狀態，同時保持選取狀態
      setGateways(prevGateways => {
        const updatedGateways = prevGateways.map(gateway => {
          const update = event.gateways.find(g => g._id === gateway._id);
          if (update) {
            return {
              ...gateway,
              status: update.status ? (update.status as GatewayStatus) : gateway.status,
              lastSeen: new Date(update.lastSeen),
              name: update.name || gateway.name,
              model: update.model || gateway.model,
              hardwareVersion: update.hardwareVersion || gateway.hardwareVersion,
              wifiFirmwareVersion: update.wifiFirmwareVersion || gateway.wifiFirmwareVersion,
              btFirmwareVersion: update.btFirmwareVersion || gateway.btFirmwareVersion,
              ipAddress: update.ipAddress || gateway.ipAddress
            };
          }
          return gateway;
        });

        // 檢查選取項目是否還存在，保持有效的選取狀態
        if (selectedItems.length > 0) {
          const existingIds = updatedGateways.map(g => g._id).filter(Boolean) as string[];
          const validSelectedItems = selectedItems.filter(id => existingIds.includes(id));

          if (validSelectedItems.length !== selectedItems.length) {
            console.log(`[網關選取保持] 清理無效選取項目: ${selectedItems.length} -> ${validSelectedItems.length}`);
            setSelectedItems(validSelectedItems);
          }
        }

        // 重新應用過濾器以更新過濾後的列表（保持分頁狀態）
        applyFiltersWithPaginationPreservation(updatedGateways, searchTerm, statusFilter, modelFilter);

        return updatedGateways;
      });

      // 網關狀態更新時，也更新統計信息
      fetchGatewayStats();
    };

    // 處理網關CRUD操作事件（新增、修改、刪除）
    const handleGatewayCRUDUpdate = (event: any) => {
      if (event.storeId !== store.id) return;

      console.log(`收到網關CRUD更新: ${event.gateways.length} 個網關, 操作類型: ${event.updateType}`);

      // 根據更新類型處理
      if (event.updateType === 'delete') {
        // 刪除操作：從本地狀態中移除
        const deletedIds = event.gateways.map((gateway: any) => gateway._id);

        // 更新主要網關列表和過濾後的列表
        setGateways(prevGateways => {
          const updatedGateways = prevGateways.filter(gateway => !deletedIds.includes(gateway._id));
          return updatedGateways;
        });

        // 同時更新過濾後的列表
        setFilteredGateways(prevFiltered => prevFiltered.filter(gateway => !deletedIds.includes(gateway._id)));

        // 清理選取狀態中被刪除的項目
        setSelectedItems(prevSelected => {
          const validSelected = prevSelected.filter(id => !deletedIds.includes(id));
          if (validSelected.length !== prevSelected.length) {
            console.log(`[網關選取保持] 清理被刪除的選取項目: ${prevSelected.length} -> ${validSelected.length}`);
          }
          return validSelected;
        });

        console.log(`已刪除 ${deletedIds.length} 個網關`);
        return;
      }

      // 創建或更新操作：重新獲取完整的網關列表以確保數據完整性
      if (event.updateType === 'create' || event.updateType === 'update') {
        console.log(`網關${event.updateType === 'create' ? '新增' : '更新'}，重新獲取網關列表`);
        fetchGateways();
      }
    };

    // 處理網關統計信息更新事件（忙碌/閒置狀態變化）
    const handleGatewayStatsUpdate = (event: GatewayStatsUpdateEvent) => {
      if (event.storeId !== store.id) return;

      console.log(`📊 收到網關統計信息更新: 總連接=${event.stats.totalConnected}, 忙碌=${event.stats.busyGateways}, 閒置=${event.stats.idleGateways}, 觸發網關=${event.triggerGatewayId}`);

      // 直接更新統計信息狀態，無需重新獲取
      setGatewayStats(event.stats);
      setStatsLoading(false);
    };

    // 訂閱網關狀態更新
    const unsubscribeStatus = subscribeToGatewayStatus(
      store.id,
      handleGatewayStatusUpdate,
      {
        includeConnectionInfo: true,
        includeFirmwareInfo: true
      }
    );

    // 訂閱網關CRUD操作更新
    const unsubscribeCRUD = subscribeToGatewayCRUD(
      store.id,
      handleGatewayCRUDUpdate,
      {}
    );

    // 訂閱網關統計信息更新（忙碌/閒置狀態變化）
    const unsubscribeStatsUpdate = subscribeToGatewayStatsUpdate(
      store.id,
      handleGatewayStatsUpdate
    );

    // 設置連接狀態為已連接
    setRealTimeConnectionStatus('connected');

    return () => {
      console.log(`取消網關即時更新訂閱: storeId=${store.id}`);
      unsubscribeStatus();
      unsubscribeCRUD();
      unsubscribeStatsUpdate();
      setRealTimeConnectionStatus('disconnected');
    };
  }, [store?.id, isRealTimeEnabled]);

  // 定期刷新網關統計信息
  useEffect(() => {
    if (!store?.id) return;

    // 立即獲取一次統計信息
    fetchGatewayStats();

    // 設置定期刷新（每30秒）
    const statsInterval = setInterval(() => {
      fetchGatewayStats();
    }, 30000);

    return () => {
      clearInterval(statsInterval);
    };
  }, [store?.id]);

  // 應用篩選器並保持分頁狀態（用於自動刷新）
  const applyFiltersWithPaginationPreservation = (
    gatewayList: Gateway[],
    search: string,
    status: GatewayStatus | '',
    model: string
  ) => {
    let filtered = [...gatewayList];

    // 搜索過濾 - 支援多欄位搜尋
    if (search) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter(
        gateway =>
          gateway.name.toLowerCase().includes(searchLower) ||
          gateway.macAddress.toLowerCase().includes(searchLower) ||
          gateway.ipAddress.toLowerCase().includes(searchLower) ||
          gateway.model.toLowerCase().includes(searchLower) ||
          gateway.hardwareVersion.toLowerCase().includes(searchLower) ||
          gateway.wifiFirmwareVersion.toLowerCase().includes(searchLower) ||
          gateway.btFirmwareVersion.toLowerCase().includes(searchLower) ||
          gateway.note?.toLowerCase().includes(searchLower)
      );
    }

    // 狀態過濾
    if (status) {
      filtered = filtered.filter(gateway => gateway.status === status);
    }

    // 模組過濾
    if (model) {
      filtered = filtered.filter(gateway => gateway.model === model);
    }

    setFilteredGateways(filtered);

    // 保持分頁狀態 - 檢查當前頁面是否仍然有效
    const newTotalPages = Math.ceil(filtered.length / itemsPerPage);
    if (currentPage > newTotalPages && newTotalPages > 0) {
      // 如果當前頁面超出了新的總頁數，調整到最後一頁
      console.log(`[網關分頁保持] 當前頁面 ${currentPage} 超出新總頁數 ${newTotalPages}，調整到最後一頁`);
      setCurrentPage(newTotalPages);
    }
    // 如果當前頁面仍然有效，則保持不變

    // 保持有效的選取狀態（只保留在篩選結果中的項目）
    if (selectedItems.length > 0) {
      const filteredGatewayIds = filtered.map(gateway => gateway._id || '').filter(id => id !== '');
      const validSelectedItems = selectedItems.filter(id => filteredGatewayIds.includes(id));

      if (validSelectedItems.length !== selectedItems.length) {
        console.log(`[網關篩選選取保持] 保持有效選取項目: ${selectedItems.length} -> ${validSelectedItems.length}`);
        setSelectedItems(validSelectedItems);
      }
    }
  };

  // 應用篩選器
  const applyFilters = (
    gatewayList: Gateway[],
    search: string,
    status: GatewayStatus | '',
    model: string
  ) => {
    let filtered = [...gatewayList];

    // 搜索過濾 - 支援多欄位搜尋
    if (search) {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter(
        gateway =>
          gateway.name.toLowerCase().includes(searchLower) ||
          gateway.macAddress.toLowerCase().includes(searchLower) ||
          gateway.ipAddress.toLowerCase().includes(searchLower) ||
          gateway.model.toLowerCase().includes(searchLower) ||
          gateway.hardwareVersion.toLowerCase().includes(searchLower) ||
          gateway.wifiFirmwareVersion.toLowerCase().includes(searchLower) ||
          gateway.btFirmwareVersion.toLowerCase().includes(searchLower) ||
          gateway.note?.toLowerCase().includes(searchLower)
      );
    }

    // 狀態過濾
    if (status) {
      filtered = filtered.filter(gateway => gateway.status === status);
    }

    // 模組過濾
    if (model) {
      filtered = filtered.filter(gateway => gateway.model === model);
    }

    setFilteredGateways(filtered);
    // 重置到第一頁
    setCurrentPage(1);

    // 保持有效的選取狀態（只保留在篩選結果中的項目）
    if (selectedItems.length > 0) {
      const filteredGatewayIds = filtered.map(gateway => gateway._id || '').filter(id => id !== '');
      const validSelectedItems = selectedItems.filter(id => filteredGatewayIds.includes(id));

      if (validSelectedItems.length !== selectedItems.length) {
        console.log(`[網關篩選選取保持] 保持有效選取項目: ${selectedItems.length} -> ${validSelectedItems.length}`);
        setSelectedItems(validSelectedItems);
      }
    }
  };

  // 處理搜索
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    applyFilters(gateways, value, statusFilter, modelFilter);
  };

  // 處理狀態過濾
  const handleStatusFilter = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value as GatewayStatus | '';
    setStatusFilter(value);
    applyFilters(gateways, searchTerm, value, modelFilter);
  };

  // 處理模組過濾
  const handleModelFilter = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setModelFilter(value);
    applyFilters(gateways, searchTerm, statusFilter, value);
  };

  // 處理編輯
  const handleEdit = (gateway: Gateway) => {
    setSelectedGateway(gateway);
    setShowEditModal(true);
  };

  // 處理刪除
  const handleDelete = async (id: string) => {
    if (!window.confirm(t('gateways.confirmDelete'))) {
      return;
    }

    try {
      // 傳遞門店ID
      await deleteGateway(id, store.id);
      fetchGateways();
      showNotification(t('gateways.deleteSuccess'), 'success');
    } catch (err: any) {
      console.error('刪除網關失敗:', err);
      showNotification(err.message || t('gateways.deleteFailed'), 'error');
    }
  };

  // 處理批量刪除
  const handleBatchDelete = async () => {
    if (selectedItems.length === 0) {
      showNotification(t('gateways.noItemSelected'), 'error');
      return;
    }

    if (!window.confirm(t('gateways.confirmBatchDelete', { count: selectedItems.length }))) {
      return;
    }

    try {
      // 依序刪除所選網關，傳遞門店ID
      for (const id of selectedItems) {
        await deleteGateway(id, store.id);
      }
      fetchGateways();
      setSelectedItems([]);
      setSelectAll(false);
      showNotification(t('gateways.batchDeleteSuccess'), 'success');
    } catch (err: any) {
      console.error('批量刪除網關失敗:', err);
      showNotification(err.message || t('gateways.batchDeleteFailed'), 'error');
    }
  };
  // 處理同步 - 獲取最新網關狀態
  const handleSync = async () => {
    try {
      setSyncingGateways(true);
      // 傳遞門店ID
      const result = await syncGateways(store.id);

      if (result.success && result.gateways) {
        // 將日期字符串轉換為 Date 對象
        const formattedGateways = result.gateways.map(gateway => ({
          ...gateway,
          lastSeen: gateway.lastSeen ? new Date(gateway.lastSeen) : null,
          createdAt: gateway.createdAt ? new Date(gateway.createdAt) : null,
          updatedAt: gateway.updatedAt ? new Date(gateway.updatedAt) : null,
        }));

        setGateways(formattedGateways);
        applyFiltersWithPaginationPreservation(formattedGateways, searchTerm, statusFilter, modelFilter);
      } else {
        // 如果沒有獲取到網關數據，則重新獲取
        fetchGateways();
      }

      // 同時更新統計信息
      fetchGatewayStats();

      showNotification(t('gateways.syncSuccess'), 'success');
    } catch (err: any) {
      console.error('同步網關狀態失敗:', err);
      showNotification(err.message || t('gateways.syncFailed'), 'error');
    } finally {
      setSyncingGateways(false);
    }
  };

  // 處理重啟網關
  const handleRestart = async (id: string) => {
    if (!window.confirm(t('gateways.confirmRestart'))) {
      return;
    }

    try {
      // 傳遞門店ID
      await restartGateway(id, store.id);
      showNotification(t('gateways.restartSuccess'), 'success');
    } catch (err: any) {
      console.error('重啟網關失敗:', err);
      showNotification(err.message || t('gateways.restartFailed'), 'error');
    }
  };

  // 複製WebSocket登入資訊
  const handleCopyWsInfo = async (gateway: Gateway) => {
    try {
      // 檢查網關ID是否有效
      if (!gateway._id) {
        showNotification('網關ID無效', 'error');
        return;
      }

      // 獲取單個網關詳情，確保有最新的WebSocket資訊
      const gatewayDetails = await getGateway(gateway._id, store.id);

      if (!gatewayDetails.websocket) {
        showNotification(t('gateways.noWsInfo'), 'error');
        return;
      }

      // 直接使用後端提供的WebSocket資訊
      const wsInfo = {
        url: gatewayDetails.websocket.url,
        path: gatewayDetails.websocket.path,
        token: gatewayDetails.websocket.token,
        protocol: gatewayDetails.websocket.protocol
      };

      const wsInfoText = JSON.stringify(wsInfo);

      // 嘗試使用現代剪貼簿 API
      if (navigator.clipboard && navigator.clipboard.writeText) {
        try {
          await navigator.clipboard.writeText(wsInfoText);
          showNotification(t('gateways.wsInfoCopied'), 'success');
          return;
        } catch (clipboardError) {
          console.warn('現代剪貼簿 API 失敗，嘗試備用方案:', clipboardError);
        }
      }

      // 備用方案：使用傳統的 document.execCommand
      try {
        const textArea = document.createElement('textarea');
        textArea.value = wsInfoText;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
          showNotification(t('gateways.wsInfoCopied'), 'success');
        } else {
          throw new Error('execCommand 複製失敗');
        }
      } catch (execCommandError) {
        console.warn('傳統剪貼簿方法也失敗:', execCommandError);

        // 最後備用方案：顯示對話框讓用戶手動複製
        const userAgent = navigator.userAgent.toLowerCase();
        const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);

        if (isMobile) {
          // 移動設備：使用 prompt 顯示內容
          prompt('請手動複製以下 WebSocket 登入資訊:', wsInfoText);
        } else {
          // 桌面設備：創建一個可選擇的文本區域
          const modal = document.createElement('div');
          modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
          `;

          const content = document.createElement('div');
          content.style.cssText = `
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 80%;
            max-height: 80%;
            overflow: auto;
          `;

          const title = document.createElement('h3');
          title.textContent = t('gateways.manualCopyTitle');
          title.style.marginBottom = '10px';

          const textarea = document.createElement('textarea');
          textarea.value = wsInfoText;
          textarea.style.cssText = `
            width: 100%;
            height: 200px;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 8px;
            resize: vertical;
          `;
          textarea.readOnly = true;

          const closeButton = document.createElement('button');
          closeButton.textContent = t('gateways.close');
          closeButton.style.cssText = `
            margin-top: 10px;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
          `;

          closeButton.onclick = () => document.body.removeChild(modal);

          content.appendChild(title);
          content.appendChild(textarea);
          content.appendChild(closeButton);
          modal.appendChild(content);
          document.body.appendChild(modal);

          // 自動選擇文本
          textarea.select();
          textarea.focus();
        }

        showNotification(t('gateways.clipboardNotAvailable'), 'warning');
      }
    } catch (err: any) {
      console.error('複製WebSocket登入資訊失敗:', err);
      showNotification(err.message || t('gateways.copyWsInfoFailed'), 'error');
    }
  };

  // 顯示通知 - 使用泡泡通知
  const showNotification = (message: string, type: 'success' | 'error' | 'warning') => {
    switch (type) {
      case 'success':
        showSuccessNotification(message);
        break;
      case 'error':
        showErrorNotification(message);
        break;
      case 'warning':
        showWarningNotification(message);
        break;
    }
  };

  // 獲取當前頁的項目
  const getCurrentPageItems = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredGateways.slice(startIndex, endIndex);
  };

  // 處理頁面變更
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // 計算總頁數
  const totalPages = Math.ceil(filteredGateways.length / itemsPerPage);

  // 監控選取狀態變化，自動更新全選狀態（只檢查當前頁）
  useEffect(() => {
    const currentPageItems = getCurrentPageItems();
    if (currentPageItems.length === 0) {
      setSelectAll(false);
      return;
    }

    // 檢查當前頁的所有網關是否都被選中
    const currentPageIds = currentPageItems.map(gateway => gateway._id || '').filter(id => id !== '');
    const allCurrentPageSelected = currentPageIds.length > 0 &&
      currentPageIds.every(id => selectedItems.includes(id));

    setSelectAll(allCurrentPageSelected);
  }, [selectedItems, currentPage, filteredGateways]);

  // 獲取唯一的模組列表
  const uniqueModels = Array.from(new Set(gateways.map(gateway => gateway.model))).filter(Boolean);

  // 渲染網關即時更新狀態指示器
  const renderRealTimeStatusIndicator = () => {
    const getStatusColor = () => {
      if (!isRealTimeEnabled) return 'text-gray-400';
      switch (realTimeConnectionStatus) {
        case 'connected': return 'text-green-500';
        case 'connecting': return 'text-yellow-500';
        case 'disconnected': return 'text-red-500';
        default: return 'text-gray-400';
      }
    };

    const getStatusText = () => {
      if (!isRealTimeEnabled) return '即時更新已關閉';
      switch (realTimeConnectionStatus) {
        case 'connected': return '即時更新已開啟';
        case 'connecting': return '正在連接...';
        case 'disconnected': return '連接已斷開';
        default: return '未知狀態';
      }
    };

    const getBgColor = () => {
      if (!isRealTimeEnabled) return 'bg-muted';
      switch (realTimeConnectionStatus) {
        case 'connected': return 'bg-green-50 dark:bg-green-900/20';
        case 'connecting': return 'bg-yellow-50 dark:bg-yellow-900/20';
        case 'disconnected': return 'bg-red-50 dark:bg-red-900/20';
        default: return 'bg-muted';
      }
    };

    const getBorderColor = () => {
      if (!isRealTimeEnabled) return 'border-border';
      switch (realTimeConnectionStatus) {
        case 'connected': return 'border-green-300 dark:border-green-700';
        case 'connecting': return 'border-yellow-300 dark:border-yellow-700';
        case 'disconnected': return 'border-red-300 dark:border-red-700';
        default: return 'border-border';
      }
    };

    return (
      <div className={`flex items-center gap-2 px-3 py-2 rounded-md border ${getBgColor()} ${getBorderColor()}`}>
        <div className={`w-2 h-2 rounded-full ${getStatusColor().replace('text-', 'bg-')}`} />
        <span className={`text-sm ${getStatusColor()}`}>
          {getStatusText()}
        </span>
        <button
          onClick={() => setIsRealTimeEnabled(!isRealTimeEnabled)}
          className="text-sm text-blue-600 hover:text-blue-800 underline ml-2"
        >
          {isRealTimeEnabled ? '關閉' : '開啟'}
        </button>
      </div>
    );
  };

  // 點擊外部關閉更多選單
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (moreMenuRef.current && !moreMenuRef.current.contains(event.target as Node)) {
        setShowMoreMenu(false);
      }
      if (
        fieldManagerRef.current &&
        !fieldManagerRef.current.contains(event.target as Node) &&
        fieldManagerButtonRef.current &&
        !fieldManagerButtonRef.current.contains(event.target as Node)
      ) {
        setShowFieldManager(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="p-8 lg:px-4 py-2">
      <div className="max-w-7xl mx-auto">
        {/* 錯誤提示 */}
        {error && (
          <div className="mb-4 p-3 bg-red-100 border-l-4 border-red-500 text-red-700 flex items-center">
            <AlertCircle className="mr-2" size={20} />
            <span>{error}</span>
          </div>
        )}



        {/* 工具列 */}
        <div className="mb-6 flex flex-wrap items-center gap-4">
          {/* 顯示當前門店名稱 */}
          <div className="px-4 py-2 bg-blue-100 text-blue-800 rounded-md border border-blue-300">
            <span className="font-medium">{store?.name || '未選擇門店'}</span>
            <span className="ml-2 text-blue-600">({store?.id || ''})</span>
          </div>

          {/* 網關即時更新狀態指示器 */}
          {renderRealTimeStatusIndicator()}
        </div>

        {/* 網關狀態統計面板 */}
        <div className="mb-6 relative">
          {/* 玻璃效果背景 */}
          <div className="absolute inset-0 bg-gradient-to-br from-sky-100/80 via-blue-50/60 to-cyan-100/80 backdrop-blur-sm rounded-2xl border border-white/30 shadow-xl"></div>

          {/* 動態光效背景 */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded-2xl animate-pulse opacity-50"></div>

          {/* 內容區域 */}
          <div className="relative p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-800 flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-sky-400 to-blue-600 rounded-lg flex items-center justify-center shadow-lg">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
                  </svg>
                </div>
                網關狀態統計
              </h3>
              <div className="flex items-center gap-3">
                <div className="text-sm text-gray-600 bg-white/40 px-3 py-1 rounded-full border border-white/30">
                  總計: {gateways.length} 個網關
                </div>
                {gatewayStats && (
                  <div className="text-sm text-gray-600 bg-white/40 px-3 py-1 rounded-full border border-white/30">
                    連線: {gatewayStats.summary.totalConnected} 個
                  </div>
                )}
                {statsLoading && (
                  <div className="text-sm text-gray-500">
                    <RefreshCw className="w-4 h-4 animate-spin inline mr-1" />
                    更新中...
                  </div>
                )}
              </div>
            </div>

            <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {/* 在線網關（整合忙碌/閒置統計） */}
              <div
                className="group relative cursor-pointer"
                onClick={() => {
                  const onlineGatewayIds = filteredGateways
                    .filter(g => g.status === 'online')
                    .map(g => g._id || '')
                    .filter(id => id !== '');
                  setSelectedItems(onlineGatewayIds);
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/20 to-green-500/20 rounded-xl border border-emerald-200/50 group-hover:shadow-lg group-hover:scale-105 transition-all duration-300"></div>
                <div className="relative p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-green-500 rounded-lg flex items-center justify-center shadow-md">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-bold text-emerald-700">在線網關</h3>
                    </div>

                    {/* 總計數字 */}
                    <div className="text-right">
                      <div className="text-3xl font-bold text-emerald-700">
                        {gateways.filter(g => g.status === 'online').length}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    {/* 忙碌網關 */}
                    <div className="text-center">
                      <div className="relative">
                        <div className="w-16 h-16 mx-auto bg-white/30 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white/40 mb-2">
                          <span className="text-2xl font-bold text-gray-800">
                            {statsLoading ? '...' : (gatewayStats?.busyGateways || 0)}
                          </span>
                        </div>
                        {/* 狀態指示器 - 紅色圓點在右上角 */}
                        <div className="absolute top-0 right-0 w-4 h-4 rounded-full bg-red-400 border-2 border-white"></div>
                      </div>
                      <span className="block text-sm font-medium text-gray-800">忙碌</span>
                    </div>

                    {/* 閒置網關 */}
                    <div className="text-center">
                      <div className="relative">
                        <div className="w-16 h-16 mx-auto bg-white/30 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white/40 mb-2">
                          <span className="text-2xl font-bold text-gray-800">
                            {statsLoading ? '...' : (gatewayStats?.idleGateways || 0)}
                          </span>
                        </div>
                        {/* 狀態指示器 - 綠色圓點在右上角 */}
                        <div className="absolute top-0 right-0 w-4 h-4 rounded-full bg-green-400 border-2 border-white"></div>
                      </div>
                      <span className="block text-sm font-medium text-gray-800">閒置</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 離線網關 */}
              <div
                className="group relative cursor-pointer"
                onClick={() => {
                  const offlineGatewayIds = filteredGateways
                    .filter(g => g.status === 'offline')
                    .map(g => g._id || '')
                    .filter(id => id !== '');
                  setSelectedItems(offlineGatewayIds);
                }}
                title="點擊選取所有離線網關"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-gray-400/20 to-slate-500/20 rounded-xl border border-gray-200/50 group-hover:shadow-lg group-hover:scale-105 transition-all duration-300"></div>
                <div className="relative p-4 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <div className="w-10 h-10 bg-gradient-to-br from-gray-400 to-slate-500 rounded-lg flex items-center justify-center shadow-md">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-gray-700 mb-1">
                    {gateways.filter(g => g.status === 'offline').length}
                  </div>
                  <div className="text-lg font-bold text-gray-700">離線網關</div>
                </div>
              </div>

              {/* 未識別模組 */}
              <div
                className="group relative cursor-pointer"
                onClick={() => {
                  const unmodeledGatewayIds = filteredGateways
                    .filter(g => !g.model)
                    .map(g => g._id || '')
                    .filter(id => id !== '');
                  setSelectedItems(unmodeledGatewayIds);
                }}
                title="點擊選取所有未識別模組的網關"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-orange-400/20 to-red-500/20 rounded-xl border border-orange-200/50 group-hover:shadow-lg group-hover:scale-105 transition-all duration-300"></div>
                <div className="relative p-4 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <div className="w-10 h-10 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg flex items-center justify-center shadow-md">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-orange-700 mb-1">
                    {gateways.filter(g => !g.model).length}
                  </div>
                  <div className="text-lg font-bold text-orange-700">未識別模組</div>
                </div>
              </div>

              {/* 網關模組數 */}
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-400/20 to-violet-500/20 rounded-xl border border-purple-200/50 group-hover:shadow-lg transition-all duration-300"></div>
                <div className="relative p-4 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-violet-500 rounded-lg flex items-center justify-center shadow-md">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                      </svg>
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-purple-700 mb-1">
                    {uniqueModels.length}
                  </div>
                  <div className="text-lg font-bold text-purple-700">網關模組數</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 搜索和篩選工具列 */}
        <div className="mb-6 flex flex-wrap items-center gap-4">

          <div className="relative flex-1">
            <input
              type="text"
              placeholder={t('gateways.searchPlaceholder')}
              value={searchTerm}
              onChange={handleSearch}
              className="w-full pl-4 pr-10 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <Search className="absolute right-3 top-2.5 text-gray-400 w-5 h-5" />
          </div>

          {/* 狀態篩選 */}
          <select
            value={statusFilter}
            onChange={handleStatusFilter}
            className="px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">{t('gateways.allStatus')}</option>
            <option value="online">{t('gateways.online')}</option>
            <option value="offline">{t('gateways.offline')}</option>
          </select>

          {/* 模組篩選 */}
          <select
            value={modelFilter}
            onChange={handleModelFilter}
            className="px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">{t('gateways.allModels')}</option>
            {uniqueModels.map(model => (
              <option key={model} value={model}>
                {model}
              </option>
            ))}
          </select>

          {/* 同步按鈕 */}
          <button
            onClick={handleSync}
            disabled={syncingGateways}
            className={`flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md ${syncingGateways ? 'opacity-75 cursor-not-allowed' : 'hover:bg-blue-600'
              }`}
          >
            <RefreshCw className={`w-5 h-5 ${syncingGateways ? 'animate-spin' : ''}`} />
            {syncingGateways ? t('gateways.syncing') : t('gateways.syncGateways')}
          </button>

          {/* 新增網關按鈕 */}
          <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center gap-2 px-4 py-2 bg-violet-500 text-white rounded-md hover:bg-violet-600"
          >
            <Plus className="w-5 h-5" />
            {t('gateways.addGateway')}
          </button>

          {/* 全選所有按鈕 */}
          <button
            onClick={() => {
              const allFilteredGatewayIds = filteredGateways.map(gateway => gateway._id || '').filter(id => id !== '');
              const allSelected = allFilteredGatewayIds.length > 0 &&
                allFilteredGatewayIds.every(id => selectedItems.includes(id));

              if (allSelected) {
                // 取消全選所有
                setSelectedItems([]);
              } else {
                // 全選所有
                setSelectedItems(allFilteredGatewayIds);
              }
            }}
            className="flex items-center gap-2 px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600"
            title={
              filteredGateways.length > 0 &&
              filteredGateways.every(gateway => selectedItems.includes(gateway._id || ''))
                ? '取消全選所有網關'
                : '全選所有網關 (跨頁)'
            }
          >
            <CheckSquare className="w-5 h-5" />
            {filteredGateways.length > 0 &&
             filteredGateways.every(gateway => selectedItems.includes(gateway._id || ''))
              ? '取消全選所有'
              : '全選所有'
            }
          </button>

          {/* 批量刪除按鈕 */}
          <button
            onClick={handleBatchDelete}
            disabled={selectedItems.length === 0}
            className={`flex items-center gap-2 px-4 py-2 bg-red-500 text-white rounded-md ${
              selectedItems.length === 0 ? 'opacity-75 cursor-not-allowed' : 'hover:bg-red-600'
            }`}
            title={selectedItems.length === 0 ? '請先選擇要刪除的網關' : `批量刪除 ${selectedItems.length} 個網關`}
          >
            <Trash2 className="w-5 h-5" />
            {selectedItems.length > 0 ? `${t('common.delete')} (${selectedItems.length})` : t('common.delete')}
          </button>

          {/* 更多操作按鈕 */}
          <div className="relative" ref={moreMenuRef}>
            <button
              onClick={() => setShowMoreMenu(!showMoreMenu)}
              className="flex items-center gap-2 px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
            >
              <MoreHorizontal className="w-5 h-5" />
              {t('common.more')}
            </button>
            {showMoreMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                <div className="py-1">
                  <button
                    onClick={() => {
                      setShowWifiUpgradeModal(true);
                      setShowMoreMenu(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Wifi className="w-4 h-4 mr-2" />
                    {t('gateways.upgradeWifiFirmware')}
                  </button>
                  <button
                    onClick={() => {
                      setShowBtUpgradeModal(true);
                      setShowMoreMenu(false);
                    }}
                    className="flex items-center w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Bluetooth className="w-4 h-4 mr-2" />
                    {t('gateways.upgradeBtFirmware')}
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* 欄位管理器 */}
          <div className="relative" ref={fieldManagerRef}>
            <button
              className="flex items-center gap-2 px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600"
              onClick={() => setShowFieldManager(!showFieldManager)}
              ref={fieldManagerButtonRef}
            >
              <Grid className="w-5 h-5" />
              {t('gateways.fieldManagement')}
            </button>
            {showFieldManager && (
              <div className="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg z-10">
                <div className="p-4">
                  <h3 className="text-lg font-semibold mb-2">{t('gateways.manageFields')}</h3>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={visibleFields.macAddress}
                        onChange={() => setVisibleFields({ ...visibleFields, macAddress: !visibleFields.macAddress })}
                        className="mr-2 rounded"
                      />
                      {t('gateways.macAddress')}
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={visibleFields.status}
                        onChange={() => setVisibleFields({ ...visibleFields, status: !visibleFields.status })}
                        className="mr-2 rounded"
                      />
                      {t('gateways.status')}
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={visibleFields.lastSeen}
                        onChange={() => setVisibleFields({ ...visibleFields, lastSeen: !visibleFields.lastSeen })}
                        className="mr-2 rounded"
                      />
                      {t('gateways.lastSeen')}
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={visibleFields.model}
                        onChange={() => setVisibleFields({ ...visibleFields, model: !visibleFields.model })}
                        className="mr-2 rounded"
                      />
                      {t('gateways.model')}
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={visibleFields.hardwareVersion}
                        onChange={() => setVisibleFields({ ...visibleFields, hardwareVersion: !visibleFields.hardwareVersion })}
                        className="mr-2 rounded"
                      />
                      {t('gateways.hardwareVersion')}
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={visibleFields.wifiFirmwareVersion}
                        onChange={() => setVisibleFields({ ...visibleFields, wifiFirmwareVersion: !visibleFields.wifiFirmwareVersion })}
                        className="mr-2 rounded"
                      />
                      {t('gateways.wifiFirmwareVersion')}
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={visibleFields.btFirmwareVersion}
                        onChange={() => setVisibleFields({ ...visibleFields, btFirmwareVersion: !visibleFields.btFirmwareVersion })}
                        className="mr-2 rounded"
                      />
                      {t('gateways.btFirmwareVersion')}
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={visibleFields.ipAddress}
                        onChange={() => setVisibleFields({ ...visibleFields, ipAddress: !visibleFields.ipAddress })}
                        className="mr-2 rounded"
                      />
                      {t('gateways.ipAddress')}
                    </label>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 網關列表 */}
        <div className="bg-card rounded-lg shadow relative">
          {/* 使用三個固定位置的表格實現左右固定，中間滾動的效果 */}
          <div className="relative overflow-hidden" style={{ maxWidth: "100%" }}>
            <div className="flex relative">
              {/* 左側固定欄位（勾選框和序號） */}
              <div className="sticky left-0 z-20 bg-card shadow-sm" style={{ width: "100px" }}>
                <table className="w-full border-separate border-spacing-0">
                  <thead>
                    <tr>
                      <th className="bg-amber-600 px-4 py-3 w-12 border-b border-gray-200 text-white">
                        <div className="flex flex-col items-center">
                          <input
                            type="checkbox"
                            className="rounded"
                            checked={selectAll}
                            onChange={(e) => {
                              const checked = e.target.checked;
                              setSelectAll(checked);
                              if (checked) {
                                // 選中當前頁的所有網關
                                const currentPageItems = getCurrentPageItems().map(gateway => gateway._id || '').filter(id => id !== '');
                                // 合併當前頁的項目到已選取的項目中
                                setSelectedItems(prev => {
                                  const newItems = [...prev];
                                  currentPageItems.forEach(id => {
                                    if (!newItems.includes(id)) {
                                      newItems.push(id);
                                    }
                                  });
                                  return newItems;
                                });
                              } else {
                                // 取消選中當前頁的所有網關
                                const currentPageItems = getCurrentPageItems().map(gateway => gateway._id || '').filter(id => id !== '');
                                setSelectedItems(prev => prev.filter(id => !currentPageItems.includes(id)));
                              }
                            }}
                            title={selectAll ? "取消當前頁全選" : "全選當前頁"}
                          />
                        </div>
                      </th>
                      <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 text-white">
                        {t('database.serialNumber')}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan={2} className="bg-card px-4 py-8 text-center text-muted-foreground">
                          <div className="p-8">
                            <RefreshCw className="w-5 h-5 animate-spin mx-auto mb-2" />
                          </div>
                        </td>
                      </tr>
                    ) : getCurrentPageItems().length === 0 ? (
                      <tr>
                        <td colSpan={2} className="bg-card px-4 py-8 text-center text-muted-foreground">
                          <div className="p-8">{t('database.noData')}</div>
                        </td>
                      </tr>
                    ) : (
                      getCurrentPageItems().map((gateway, index) => (
                        <tr key={`left-${gateway._id}`} className={`h-[53px] ${index % 2 === 0 ? 'bg-amber-50 dark:bg-amber-900/20' : 'bg-blue-50 dark:bg-blue-900/20'}`}>
                          <td className={`px-4 py-3 border-b border-gray-200 align-middle`}>
                            <input
                              type="checkbox"
                              className="rounded"
                              checked={selectedItems.includes(gateway._id || '')}
                              onChange={(e) => {
                                if (e.target.checked && gateway._id) {
                                  setSelectedItems([...selectedItems, gateway._id]);
                                } else {
                                  setSelectedItems(selectedItems.filter(id => id !== gateway._id));
                                  setSelectAll(false);
                                }
                              }}
                            />
                          </td>
                          <td className={`px-4 py-3 border-b border-gray-200 whitespace-nowrap`}>
                            {index + 1 + (currentPage - 1) * itemsPerPage}
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {/* 中間可滾動區域（動態欄位） */}
              <div className="flex-1 overflow-x-auto">
                <table className="w-full border-separate border-spacing-0">
                  <thead>
                    <tr>
                      <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                        {t('gateways.name')}
                      </th>
                      {visibleFields.macAddress && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('gateways.macAddress')}
                        </th>
                      )}
                      {visibleFields.status && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[100px] text-white">
                          {t('gateways.status')}
                        </th>
                      )}
                      {visibleFields.lastSeen && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[180px] text-white">
                          {t('gateways.lastSeen')}
                        </th>
                      )}
                      {visibleFields.model && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[120px] text-white">
                          {t('gateways.model')}
                        </th>
                      )}
                      {visibleFields.hardwareVersion && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('gateways.hardwareVersion')}
                        </th>
                      )}
                      {visibleFields.wifiFirmwareVersion && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('gateways.wifiFirmwareVersion')}
                        </th>
                      )}
                      {visibleFields.btFirmwareVersion && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('gateways.btFirmwareVersion')}
                        </th>
                      )}
                      {visibleFields.ipAddress && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('gateways.ipAddress')}
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan={Object.values(visibleFields).filter(Boolean).length + 1} className="bg-card px-4 py-8 text-center text-muted-foreground">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : getCurrentPageItems().length === 0 ? (
                      <tr>
                        <td colSpan={Object.values(visibleFields).filter(Boolean).length + 1} className="bg-card px-4 py-8 text-center text-muted-foreground">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : (
                      getCurrentPageItems().map((gateway, index) => (
                        <tr key={`mid-${gateway._id}`} className={`h-[53px] ${index % 2 === 0 ? 'bg-amber-50 dark:bg-amber-900/20' : 'bg-blue-50 dark:bg-blue-900/20'}`}>
                          <td className="px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                            {gateway.name}
                          </td>
                          {visibleFields.macAddress && (
                            <td className="px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                              {gateway.macAddress}
                            </td>
                          )}
                          {visibleFields.status && (
                            <td className="px-4 py-3 border-b border-gray-200 min-w-[100px] align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                              <GatewayStatusBadge status={gateway.status} />
                            </td>
                          )}
                          {visibleFields.lastSeen && (
                            <td className="px-4 py-3 border-b border-gray-200 min-w-[180px] align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                              {gateway.lastSeen
                                ? new Date(gateway.lastSeen).toLocaleString()
                                : t('common.never')}
                            </td>
                          )}
                          {visibleFields.model && (
                            <td className="px-4 py-3 border-b border-gray-200 min-w-[120px] align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                              {gateway.model}
                            </td>
                          )}
                          {visibleFields.hardwareVersion && (
                            <td className="px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                              {gateway.hardwareVersion}
                            </td>
                          )}
                          {visibleFields.wifiFirmwareVersion && (
                            <td className="px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                              {gateway.wifiFirmwareVersion}
                            </td>
                          )}
                          {visibleFields.btFirmwareVersion && (
                            <td className="px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                              {gateway.btFirmwareVersion}
                            </td>
                          )}
                          {visibleFields.ipAddress && (
                            <td className="px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis">
                              {gateway.ipAddress}
                            </td>
                          )}
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {/* 右側固定區域（操作欄） */}
              <div className="sticky right-0 z-20 bg-white shadow-sm" style={{ width: "150px" }}>
                <table className="w-full border-separate border-spacing-0">
                  <thead>
                    <tr>
                      <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap text-white">
                        {t('gateways.actions')}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : getCurrentPageItems().length === 0 ? (
                      <tr>
                        <td className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : (
                      getCurrentPageItems().map((gateway, index) => (
                        <tr key={`right-${gateway._id}`} className={`h-[53px] align-middle ${index % 2 === 0 ? 'bg-amber-50' : 'bg-blue-50'}`}>
                          <td className="px-4 py-3 border-b border-gray-200 whitespace-nowrap overflow-hidden text-ellipsis">
                            <div className="flex items-center gap-2">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (gateway._id) handleRestart(gateway._id);
                                }}
                                className="text-gray-500 hover:text-blue-600"
                                title={t('gateways.restart')}
                              >
                                <Power className="w-5 h-5" />
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEdit(gateway);
                                }}
                                className="text-gray-500 hover:text-blue-600"
                                title={t('common.edit')}
                              >
                                <Edit className="w-5 h-5" />
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (gateway._id) handleDelete(gateway._id);
                                }}
                                className="text-gray-500 hover:text-red-600"
                                title={t('common.delete')}
                              >
                                <Trash className="w-5 h-5" />
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleCopyWsInfo(gateway);
                                }}
                                className="text-gray-500 hover:text-green-600"
                                title={t('gateways.copyWsInfo')}
                              >
                                <Key className="w-5 h-5" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* 分頁 */}
          {!loading && filteredGateways.length > 0 && (
            <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200">
              <div className="flex flex-col gap-1">
                <p className="text-sm text-gray-700">
                  {t('common.showing')}
                  <span className="font-medium mx-1">
                    {filteredGateways.length === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1}
                  </span>
                  {t('common.to')}
                  <span className="font-medium mx-1">
                    {Math.min(currentPage * itemsPerPage, filteredGateways.length)}
                  </span>
                  {t('common.of')}
                  <span className="font-medium mx-1">{filteredGateways.length}</span>
                  {t('common.entries')}
                </p>
                {/* 選取狀態信息 */}
                {selectedItems.length > 0 && (
                  <p className="text-sm text-blue-600 font-medium">
                    已選取 {selectedItems.length} 個網關
                    {selectedItems.length > itemsPerPage && ' (跨頁選取)'}
                  </p>
                )}
              </div>

              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  {/* 上一頁按鈕 */}
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">{t('common.previousPage')}</span>
                    &laquo;
                  </button>

                  {/* 頁碼按鈕 */}
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(pageNum => (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium
                        ${currentPage === pageNum
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                    >
                      {pageNum}
                    </button>
                  ))}

                  {/* 下一頁按鈕 */}
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">{t('common.nextPage')}</span>
                    &raquo;
                  </button>
                </nav>
              </div>
            </div>
          )}
        </div>

      </div>


      {/* 新增網關模態窗口 */}
      <AddGatewayModal
        isOpen={showAddModal}
        storeId={store.id} // 傳遞門店ID
        onClose={() => setShowAddModal(false)}
        onSuccess={() => {
          fetchGateways();
          showNotification('網關新增成功', 'success');
        }}
      />

      {/* 編輯網關模態窗口 */}
      <EditGatewayModal
        isOpen={showEditModal}
        gateway={selectedGateway}
        storeId={store.id} // 傳遞門店ID
        onClose={() => {
          setShowEditModal(false);
          setSelectedGateway(null);
        }}
        onSuccess={() => {
          fetchGateways();
          showNotification('網關更新成功', 'success');
        }}
      />

      {/* WiFi 固件升級模態窗口 */}
      <UpgradeWifiFirmwareModal
        isOpen={showWifiUpgradeModal}
        selectedGateways={selectedItems.length > 0 ? selectedItems : []}
        storeId={store.id} // 傳遞門店ID
        onClose={() => setShowWifiUpgradeModal(false)}
        onSuccess={() => {
          fetchGateways();
          showNotification('WiFi 固件升級成功', 'success');
        }}
      />

      {/* 藍芽固件升級模態窗口 */}
      <UpgradeBtFirmwareModal
        isOpen={showBtUpgradeModal}
        selectedGateways={selectedItems.length > 0 ? selectedItems : []}
        storeId={store.id} // 傳遞門店ID
        onClose={() => setShowBtUpgradeModal(false)}
        onSuccess={() => {
          fetchGateways();
          showNotification('藍芽固件升級成功', 'success');
        }}
      />
    </div>
  );
}
