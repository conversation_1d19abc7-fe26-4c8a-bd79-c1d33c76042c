#!/usr/bin/env python3
"""
Bin檔案格式化工具測試腳本
"""

import os
import tempfile
import struct
import zlib
from bin_formatter import BinFormatter

def create_test_bin_file(content: bytes = b"test binary data") -> str:
    """創建測試用的bin檔案"""
    with tempfile.NamedTemporaryFile(suffix='.bin', delete=False) as f:
        f.write(content)
        return f.name

def test_basic_functionality():
    """測試基本功能"""
    print("=== 測試基本功能 ===")
    
    formatter = BinFormatter()
    
    # 創建測試檔案
    test_data = b"Hello, this is test firmware data!"
    test_bin_path = create_test_bin_file(test_data)
    
    try:
        # 測試格式化 (使用模型名稱)
        output_path = formatter.format_bin_file(
            bin_path=test_bin_path,
            device_type="gateway",
            function_type="wifi",
            version="1.2.3.4",
            model="GW-Pro-V1",
            min_hw_version="1.0.0.0",
            max_hw_version="2.0.0.0"
        )

        print(f"✅ 成功生成檔案: {os.path.basename(output_path)}")

        # 使用新的解析方法驗證輸出檔案格式
        try:
            parsed_result = formatter.parse_bin_file(output_path)

            print(f"  設備類型: {parsed_result['device_type']} (應為gateway)")
            print(f"  模型名稱: {parsed_result['model']} (應為GW-Pro-V1)")
            print(f"  功能類型: {parsed_result['function_type']} (應為wifi)")
            print(f"  韌體版本: {parsed_result['version']} (應為1.2.3.4)")
            print(f"  最小硬體版本: {parsed_result['min_hw_version']} (應為1.0.0.0)")
            print(f"  最大硬體版本: {parsed_result['max_hw_version']} (應為2.0.0.0)")
            print(f"  bin內容長度: {parsed_result['bin_size']} bytes")
            print(f"  校驗和: {parsed_result['checksum']}")
            print(f"  計算校驗和: {parsed_result['calculated_checksum']}")

            # 驗證解析結果
            if parsed_result['is_valid']:
                print("  ✅ 校驗和正確")
            else:
                print("  ❌ 校驗和錯誤")

            # 驗證bin內容
            if parsed_result['bin_data'] == test_data:
                print("  ✅ bin內容正確")
            else:
                print("  ❌ bin內容不匹配")

            # 驗證各個欄位
            if (parsed_result['device_type'] == 'gateway' and
                parsed_result['model'] == 'GW-Pro-V1' and
                parsed_result['function_type'] == 'wifi' and
                parsed_result['version'] == '1.2.3.4'):
                print("  ✅ 所有欄位解析正確")
            else:
                print("  ❌ 部分欄位解析錯誤")

        except Exception as e:
            print(f"  ❌ 解析失敗: {e}")
            
    finally:
        # 清理測試檔案
        if os.path.exists(test_bin_path):
            os.unlink(test_bin_path)

def test_validation():
    """測試驗證功能"""
    print("\n=== 測試驗證功能 ===")
    
    formatter = BinFormatter()
    test_bin_path = create_test_bin_file()
    
    try:
        # 測試不支援的設備類型
        try:
            formatter.format_bin_file(test_bin_path, "invalid_device", "wifi", "1.0.0.0", "GW-Test", "1.0.0.0", "2.0.0.0")
            print("❌ 應該拒絕無效設備類型")
        except ValueError as e:
            print(f"✅ 正確拒絕無效設備類型: {e}")

        # 測試不支援的功能類型
        try:
            formatter.format_bin_file(test_bin_path, "gateway", "invalid_function", "1.0.0.0", "GW-Test", "1.0.0.0", "2.0.0.0")
            print("❌ 應該拒絕無效功能類型")
        except ValueError as e:
            print(f"✅ 正確拒絕無效功能類型: {e}")

        # 測試設備功能不匹配
        try:
            formatter.format_bin_file(test_bin_path, "epd", "wifi", "1.0.0.0", "EPD-Large", "1.0.0.0", "2.0.0.0")
            print("❌ 應該拒絕EPD使用WiFi")
        except ValueError as e:
            print(f"✅ 正確拒絕EPD使用WiFi: {e}")

        # 測試空模型名稱
        try:
            formatter.format_bin_file(test_bin_path, "gateway", "wifi", "1.0.0.0", "", "1.0.0.0", "2.0.0.0")
            print("❌ 應該拒絕空模型名稱")
        except ValueError as e:
            print(f"✅ 正確拒絕空模型名稱: {e}")

        # 測試無效硬體版本格式
        try:
            formatter.format_bin_file(test_bin_path, "gateway", "wifi", "1.0.0.0", "GW-Test", "invalid", "2.0.0.0")
            print("❌ 應該拒絕無效硬體版本格式")
        except ValueError as e:
            print(f"✅ 正確拒絕無效硬體版本格式: {e}")

        # 測試無效版本格式
        invalid_versions = ["1.0.0", "1.0.0.0.0", "a.b.c.d", "1.0"]
        for version in invalid_versions:
            try:
                formatter.format_bin_file(test_bin_path, "gateway", "wifi", version, "GW-Test", "1.0.0.0", "2.0.0.0")
                print(f"❌ 應該拒絕無效版本格式: {version}")
            except ValueError as e:
                print(f"✅ 正確拒絕無效版本格式 {version}: {e}")

        # 測試不存在的檔案
        try:
            formatter.format_bin_file("nonexistent.bin", "gateway", "wifi", "1.0.0.0", "GW-Test", "1.0.0.0", "2.0.0.0")
            print("❌ 應該拒絕不存在的檔案")
        except FileNotFoundError as e:
            print(f"✅ 正確拒絕不存在的檔案: {e}")
            
    finally:
        if os.path.exists(test_bin_path):
            os.unlink(test_bin_path)

def test_all_combinations():
    """測試所有有效的設備和功能組合"""
    print("\n=== 測試所有有效組合 ===")
    
    formatter = BinFormatter()
    test_bin_path = create_test_bin_file()
    
    valid_combinations = [
        ("gateway", "wifi", "GW-WiFi-Pro"),
        ("gateway", "ble", "GW-BLE-Standard"),
        ("epd", "ble", "EPD-4K-Display")
    ]

    try:
        for device_type, function_type, model in valid_combinations:
            output_path = formatter.format_bin_file(
                test_bin_path, device_type, function_type, "2.1.0.5", model, "1.0.0.0", "3.0.0.0"
            )
            print(f"✅ {device_type} ({model}) + {function_type}: {os.path.basename(output_path)}")
            
    finally:
        if os.path.exists(test_bin_path):
            os.unlink(test_bin_path)

def main():
    """執行所有測試"""
    print("開始測試 Bin檔案格式化工具...")
    
    test_basic_functionality()
    test_validation()
    test_all_combinations()
    
    print("\n=== 測試完成 ===")
    print("請檢查 output/ 目錄中的生成檔案")

if __name__ == "__main__":
    main()
