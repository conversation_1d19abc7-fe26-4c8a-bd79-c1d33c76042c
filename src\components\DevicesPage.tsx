import React, { useState, useEffect, useRef } from 'react';
import { Search, RefreshCw, Trash2, Edit, AlertCircle, Battery, Signal, Wifi, Link2, Link, Grid, Trash, Send, CheckSquare } from 'lucide-react';
import { Device, DeviceStatus } from '../types/device';
import { getAllDevices, deleteDevice, syncDevices, sendDevicePreviewToGateway, updateDevice, sendMultipleDevicePreviewsToGateways } from '../utils/api/deviceApi';
import { DeviceStatusBadge } from './ui/DeviceStatusBadge';
// 使用絕對路徑導入
import { ImageUpdateStatusBadge } from '../components/ui/ImageUpdateStatusBadge';
import { EditDeviceModal } from './EditDeviceModal';
import BindDeviceDataModal from './BindDeviceDataModal';
import { saveDeviceFieldsViewConfig, getDeviceFieldsViewConfig, DEFAULT_DEVICE_FIELDS_VIEW_CONFIG } from '../utils/api/deviceConfigApi';
import { buildEndpointUrl } from '../utils/api/apiConfig';
import { Template, DisplayColorType } from '../types';
import { getScreenConfigs } from '../screens/screenSizeMap';
import { useTranslation } from 'react-i18next';
import { Store, StoreSpecificData } from '../types/store';
import { getAllStoreData } from '../utils/api/storeDataApi';
import { useAuthStore } from '../store/authStore';
import { regeneratePreviewBeforeSend } from '../utils/previewImageManager';
import { showSuccessNotification, showErrorNotification } from '../utils/bubbleNotification';
import { BatchSendProgress } from './BatchSendProgress';
import { cancelBatchSend, subscribeToDeviceStatus, subscribeToDeviceCRUD, DeviceStatusEvent } from '../utils/websocketClient';

import ColorTypeGradient from './ui/ColorTypeGradient';

// 智能網關動態波光幕樣式
const smartGatewayStyles = `
  .smart-gateway-wave-border {
    position: relative;
    display: inline-block;
    padding: 1px;
    border-radius: 4px;
    background: linear-gradient(90deg,
      rgba(59, 130, 246, 0.8),
      rgba(147, 51, 234, 0.8),
      rgba(236, 72, 153, 0.8),
      rgba(59, 130, 246, 0.8));
    background-size: 300% 100%;
    animation: wave-flow 2.5s ease-in-out infinite;
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
    height: 24px;
    line-height: 24px;
    overflow: hidden;
  }

  .smart-gateway-wave-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent);
    animation: wave-shimmer 2s ease-in-out infinite;
    border-radius: 4px;
  }

  .smart-gateway-mac-text {
    display: block;
    background: linear-gradient(90deg,
      rgba(255, 255, 255, 0.95),
      rgba(248, 250, 252, 0.95),
      rgba(255, 255, 255, 0.95));
    background-size: 200% 100%;
    animation: text-wave 2.5s ease-in-out infinite;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 500;
    color: #1e40af;
    position: relative;
    z-index: 2;
    height: 22px;
    line-height: 18px;
    font-size: 14px;
    text-shadow: 0 0 2px rgba(255, 255, 255, 0.8);
  }

  .smart-gateway-mac-container {
    height: 24px;
    display: flex;
    align-items: center;
  }

  @keyframes wave-flow {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes wave-shimmer {
    0% {
      left: -100%;
    }
    50% {
      left: 100%;
    }
    100% {
      left: 100%;
    }
  }

  @keyframes text-wave {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
`;

interface DevicesPageProps {
  store: Store | null;
  onViewDeviceDetail?: (device: Device) => void;
}

export function DevicesPage({ store, onViewDeviceDetail }: DevicesPageProps) {
  const { t } = useTranslation();
  const [devices, setDevices] = useState<Device[]>([]);
  const [filteredDevices, setFilteredDevices] = useState<Device[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<DeviceStatus | ''>('');
  const [sizeFilter, setSizeFilter] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showBindModal, setShowBindModal] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [syncingDevices, setSyncingDevices] = useState(false);
  const [sendingDevices, setSendingDevices] = useState<Set<string>>(new Set()); // 追蹤正在發送的設備ID
  const [batchSending, setBatchSending] = useState(false); // 追蹤批量發送狀態
  const [showBatchProgress, setShowBatchProgress] = useState(false); // 顯示批量傳送進度
  const [currentBatchId, setCurrentBatchId] = useState<string | null>(null); // 當前批量傳送ID
  const [templates, setTemplates] = useState<Template[]>([]);
  const [templateMap, setTemplateMap] = useState<Record<string, string>>({});
  const [storeData, setStoreData] = useState<StoreSpecificData[]>([]);

  // 即時更新相關狀態
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true);
  const [realTimeConnectionStatus, setRealTimeConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('disconnected');

  const [visibleFields, setVisibleFields] = useState<Record<string, boolean>>({
    macAddress: true,
    model: true,
    hardwareVersion: true,
    firmwareVersion: true, // 添加 firmwareVersion 欄位
    size: true,
    rssi: true,
    battery: true,
    status: true,
    lastSeen: true,
    code: true,
    note: true,
    dataId: true,
    templateId: true,
    colorType: true, // 添加 colorType 欄位
    imageUpdateStatus: true, // 添加圖片更新狀態欄位
  });

  const [showFieldManager, setShowFieldManager] = useState(false);
  const fieldManagerRef = useRef<HTMLDivElement>(null);
  const fieldManagerButtonRef = useRef<HTMLButtonElement>(null);

  // 處理欄位管理器外部點擊關閉
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        showFieldManager &&
        fieldManagerRef.current &&
        !fieldManagerRef.current.contains(event.target as Node) &&
        fieldManagerButtonRef.current &&
        !fieldManagerButtonRef.current.contains(event.target as Node)
      ) {
        // 關閉設定視窗前自動保存設定
        saveFieldsViewSettings();
        setShowFieldManager(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showFieldManager, visibleFields]);
  // 處理欄位顯示切換
  const toggleFieldVisibility = (fieldId: string) => {
    // 如果是 MAC 地址欄位，則不允許取消
    if (fieldId === 'macAddress') {
      return;
    }

    setVisibleFields(prev => ({
      ...prev,
      [fieldId]: !prev[fieldId]
    }));
  };

  // 檢查是否所有欄位都被選中
  const areAllFieldsSelected = () => {
    // 排除 macAddress 欄位後，檢查其他欄位是否全都選中
    const otherFields = Object.entries(visibleFields).filter(([key]) => key !== 'macAddress');
    return otherFields.length > 0 && otherFields.every(([_, isVisible]) => isVisible);
  };

  // 全選或取消全選所有欄位
  const toggleAllFields = () => {
    const allSelected = areAllFieldsSelected();
    const newVisibility = Object.keys(visibleFields).reduce((acc, field) => {
      // MAC 地址欄位始終保持選中狀態
      if (field === 'macAddress') {
        acc[field] = true;
      } else {
        acc[field] = !allSelected;
      }
      return acc;
    }, {} as Record<string, boolean>);
    setVisibleFields(newVisibility);
  };

  // 保存欄位顯示設定
  const saveFieldsViewSettings = async () => {
    try {
      // 將 Record<string, boolean> 格式轉換為欄位 ID 數組格式
      const visibleFieldsArray = Object.entries(visibleFields)
        .filter(([_, isVisible]) => isVisible)
        .map(([fieldId]) => fieldId);

      console.log('保存設備欄位設定:', visibleFieldsArray);

      await saveDeviceFieldsViewConfig({
        visibleFields: visibleFieldsArray,
        columnOrder: [], // 目前未實現排序功能
        columnWidths: {} // 目前未實現寬度調整功能
      });

      console.log('設備欄位設定保存成功');
      showNotification('欄位顯示設定已保存', 'success');
    } catch (error) {
      console.error('保存設備欄位設定失敗:', error);
      showNotification('保存欄位設定失敗', 'error');
    }
  };

  // 獲取設備尺寸對應的 DisplayColorType
  const getDeviceColorType = (deviceSize: string | undefined): DisplayColorType | undefined => {
    if (!deviceSize) return undefined;

    // 獲取所有屏幕配置
    const screenConfigs = getScreenConfigs();

    // 標準化尺寸字符串，移除非數字和點的字符
    let normalizedSize = deviceSize.replace(/[^0-9.]/g, '');

    // 尋找匹配的屏幕配置
    for (const config of screenConfigs) {
      // 標準化配置名稱
      const configName = config.name.replace(/[^0-9.]/g, '');

      if (configName === normalizedSize) {
        // 如果找到匹配的配置，返回其支持的第一個顏色類型
        return config.supportedColors[0];
      }
    }

    // 如果找不到匹配的配置，返回默認值
    return DisplayColorType.BW;
  };

  // 獲取設備列表
  const fetchDevices = async () => {
    try {
      setLoading(true);
      setError(null);

      // 如果有門店ID，則按門店ID過濾設備
      const deviceList = await getAllDevices(store?.id);

      // 將日期字符串轉換為 Date 對象
      const formattedDevices = deviceList.map(device => ({
        ...device,
        lastSeen: device.lastSeen ? new Date(device.lastSeen) : null,
        createdAt: device.createdAt ? new Date(device.createdAt) : null,
        updatedAt: device.updatedAt ? new Date(device.updatedAt) : null,
      }));

      // 檢查並設置每個設備的 colorType
      for (const device of formattedDevices) {
        if (device._id && device.data && !device.data.colorType && device.data.size) {
          const colorType = getDeviceColorType(device.data.size);
          if (colorType) {
            try {
              await updateDevice(device._id, {
                data: {
                  ...device.data,
                  colorType: colorType
                }
              });
              console.log(`已根據設備尺寸設置 colorType: ${colorType}`);
              // 更新本地設備數據
              device.data.colorType = colorType;
            } catch (error) {
              console.error('設置 colorType 失敗:', error);
            }
          }
        }
      }

      setDevices(formattedDevices);
      applyFiltersWithPaginationPreservation(formattedDevices, searchTerm, statusFilter, sizeFilter);
    } catch (err: any) {
      console.error('獲取設備列表失敗:', err);
      setError(err.message || '獲取設備列表失敗');
    } finally {
      setLoading(false);
    }
  };  // 獲取模板列表
  const fetchTemplates = async () => {
    try {
      // 從 authStore 獲取 token
      const { token } = useAuthStore.getState();

      // 構建API URL，如果有門店ID則添加到查詢參數
      let apiUrl = buildEndpointUrl('templates');
      if (store?.id) {
        apiUrl += `?storeId=${encodeURIComponent(store.id)}`;
        console.log(`按門店ID過濾模板: ${store.id}`);
      }

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
        credentials: 'include', // 包含 cookie
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('未登入或登入已過期');
        }
        throw new Error(`獲取模板列表失敗: ${response.status}`);
      }
      const templateList = await response.json();
      setTemplates(templateList);

      // 創建模板ID到模板名稱的映射
      const map: Record<string, string> = {};
      templateList.forEach((template: Template) => {
        map[template.id] = template.name;
      });
      setTemplateMap(map);
    } catch (err) {
      console.error('獲取模板列表失敗:', err);
    }
  };

  // 獲取門店數據
  const fetchStoreData = async () => {
    if (!store?.id) return;

    try {
      console.log(`正在獲取門店 ${store.id} 的數據...`);
      const data = await getAllStoreData(store.id);
      console.log(`獲取到門店數據，數量: ${data.length}`);
      setStoreData(data);
    } catch (error) {
      console.error('獲取門店數據失敗:', error);
    }
  };

  // 注入智能網關樣式
  useEffect(() => {
    const styleId = 'smart-gateway-styles';
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = smartGatewayStyles;
      document.head.appendChild(style);
    }

    return () => {
      const existingStyle = document.getElementById(styleId);
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  }, []);

  // 初始加載
  useEffect(() => {
    loadFieldsViewSettings();
  }, []);

  // 注意：舊的 deviceUpdated 事件監聽器已移除，現在使用WebSocket實時更新

  // 當門店變化時重新獲取所有數據
  useEffect(() => {
    if (store?.id) {
      console.log(`門店變更為 ${store.id}，重新獲取設備列表、模板和門店數據`);
      fetchDevices();
      fetchTemplates();
      fetchStoreData();
    }
  }, [store?.id]);

  // 設備狀態即時更新Hook
  useEffect(() => {
    if (!store?.id || !isRealTimeEnabled) return;

    console.log(`啟用設備即時更新: storeId=${store.id}`);
    setRealTimeConnectionStatus('connecting');

    // 處理設備狀態更新事件（保持選取狀態）
    const handleDeviceStatusUpdate = (event: DeviceStatusEvent) => {
      if (event.storeId !== store.id) return;

      console.log(`收到設備狀態更新: ${event.devices.length} 個設備, 更新類型: ${event.updateType || 'status'}`);

      // 注意：DeviceStatusEvent的updateType只有'single'和'batch'，沒有'delete'
      // 這裡主要處理網關回報的設備狀態更新

      // 創建或更新操作：選擇性更新設備狀態，同時保持選取狀態
      setDevices(prevDevices => {
        const updatedDevices = prevDevices.map(device => {
          const update = event.devices.find(d => d._id === device._id);
          if (update) {
            return {
              ...device,
              macAddress: update.macAddress || device.macAddress,
              status: update.status as DeviceStatus,
              lastSeen: new Date(update.lastSeen),
              imageUpdateStatus: update.imageUpdateStatus || device.imageUpdateStatus,
              data: {
                ...device.data,
                battery: update.data?.battery ?? device.data?.battery,
                rssi: update.data?.rssi ?? device.data?.rssi,
                imageCode: update.data?.imageCode ?? device.data?.imageCode,
                ...update.data
              }
            };
          }
          return device;
        });

        // 檢查是否有新設備需要添加
        const newDevices = event.devices.filter(eventDevice =>
          !prevDevices.some(existingDevice => existingDevice._id === eventDevice._id)
        );

        if (newDevices.length > 0) {
          console.log(`發現 ${newDevices.length} 個新設備，重新獲取完整設備列表`);
          // 對於新增操作，重新獲取完整的設備列表以確保數據完整性
          fetchDevices();
          return updatedDevices;
        }

        // 檢查選取項目是否還存在，保持有效的選取狀態
        if (selectedItems.length > 0) {
          const existingIds = updatedDevices.map(d => d._id).filter(Boolean);
          const validSelectedItems = selectedItems.filter(id => existingIds.includes(id));

          if (validSelectedItems.length !== selectedItems.length) {
            console.log(`[設備選取保持] 清理無效選取項目: ${selectedItems.length} -> ${validSelectedItems.length}`);
            setSelectedItems(validSelectedItems);
          }
        }

        return updatedDevices;
      });

      // 同時更新過濾後的設備列表，保持選取狀態
      setFilteredDevices(prevFiltered => {
        return prevFiltered.map(device => {
          const update = event.devices.find(d => d._id === device._id);
          if (update) {
            return {
              ...device,
              macAddress: update.macAddress || device.macAddress,
              status: update.status as DeviceStatus,
              lastSeen: new Date(update.lastSeen),
              imageUpdateStatus: update.imageUpdateStatus || device.imageUpdateStatus,
              data: {
                ...device.data,
                battery: update.data?.battery ?? device.data?.battery,
                rssi: update.data?.rssi ?? device.data?.rssi,
                imageCode: update.data?.imageCode ?? device.data?.imageCode,
                ...update.data
              }
            };
          }
          return device;
        });
      });
    };

    // 處理設備CRUD操作事件（新增、修改、刪除）
    const handleDeviceCRUDUpdate = (event: any) => {
      if (event.storeId !== store.id) return;

      console.log(`收到設備CRUD更新: ${event.devices.length} 個設備, 操作類型: ${event.updateType}`);

      // 根據更新類型處理
      if (event.updateType === 'delete') {
        // 刪除操作：從本地狀態中移除
        const deletedIds = event.devices.map((device: any) => device._id);
        setDevices(prevDevices => prevDevices.filter(device => !deletedIds.includes(device._id)));
        setFilteredDevices(prevFiltered => prevFiltered.filter(device => !deletedIds.includes(device._id)));

        // 清理選取狀態中被刪除的項目
        setSelectedItems(prevSelected => {
          const validSelected = prevSelected.filter(id => !deletedIds.includes(id));
          if (validSelected.length !== prevSelected.length) {
            console.log(`[設備選取保持] 清理被刪除的選取項目: ${prevSelected.length} -> ${validSelected.length}`);
          }
          return validSelected;
        });

        console.log(`已刪除 ${deletedIds.length} 個設備`);
        return;
      }

      // 創建或更新操作：重新獲取完整的設備列表以確保數據完整性
      if (event.updateType === 'create' || event.updateType === 'update') {
        console.log(`設備${event.updateType === 'create' ? '新增' : '更新'}，重新獲取設備列表`);
        fetchDevices();
      }
    };

    // 訂閱設備狀態更新
    const unsubscribeStatus = subscribeToDeviceStatus(
      store.id,
      handleDeviceStatusUpdate,
      {
        includeImageStatus: true,
        includeBatteryInfo: true
      }
    );

    // 訂閱設備CRUD操作更新
    const unsubscribeCRUD = subscribeToDeviceCRUD(
      store.id,
      handleDeviceCRUDUpdate,
      {}
    );

    // 設置連接狀態為已連接
    setRealTimeConnectionStatus('connected');

    return () => {
      console.log(`取消設備即時更新訂閱: storeId=${store.id}`);
      unsubscribeStatus();
      unsubscribeCRUD();
      setRealTimeConnectionStatus('disconnected');
    };
  }, [store?.id, isRealTimeEnabled]);
  // 載入欄位顯示設定
  const loadFieldsViewSettings = async () => {
    try {
      try {
        const savedConfig = await getDeviceFieldsViewConfig();

        if (savedConfig && savedConfig.visibleFields !== undefined) {
          console.log('已載入保存的設備欄位設定:', savedConfig.visibleFields);

          // 將數組格式的 visibleFields 轉換為 Record<string, boolean> 格式
          const initialFieldKeys = Object.keys(visibleFields);
          const savedFieldsVisibility = initialFieldKeys.reduce((acc, field) => {
            acc[field] = savedConfig.visibleFields.includes(field);
            return acc;
          }, {} as Record<string, boolean>);

          setVisibleFields(savedFieldsVisibility);
          return;
        }
      } catch (configErr: any) {
        // 如果是 404 錯誤，表示後端尚未實現此端點，使用默認配置
        if (configErr.message && (configErr.message.includes('Not Found') || configErr.message.includes('404'))) {
          console.log('設備欄位設定API尚未實現，使用默認配置');
        } else {
          console.error('載入設備欄位設定失敗:', configErr);
        }
      }

      // 如果沒有設定或發生錯誤，使用默認設定
      console.log('使用默認設備欄位設定');
      const defaultVisibility = DEFAULT_DEVICE_FIELDS_VIEW_CONFIG.visibleFields.reduce((acc, field) => {
        acc[field] = true;
        return acc;
      }, {} as Record<string, boolean>);

      setVisibleFields(defaultVisibility);
    } catch (err) {
      console.error('設定欄位視圖時發生未預期的錯誤:', err);
      // 發生錯誤時使用當前的設定
    }
  };

  // 應用篩選器並保持分頁狀態（用於自動刷新）
  const applyFiltersWithPaginationPreservation = (
    deviceList: Device[],
    search: string,
    status: DeviceStatus | '',
    size: string
  ) => {
    let result = [...deviceList];

    // 搜尋條件篩選 - 支援多欄位搜尋
    if (search) {
      const lowerSearch = search.toLowerCase();
      result = result.filter(
        device =>
          device.macAddress.toLowerCase().includes(lowerSearch) ||
          device.model?.toLowerCase().includes(lowerSearch) ||
          device.hardwareVersion?.toLowerCase().includes(lowerSearch) ||
          device.dataId?.toLowerCase().includes(lowerSearch) ||
          device.note?.toLowerCase().includes(lowerSearch) ||
          device.code?.toLowerCase().includes(lowerSearch) ||
          device.data?.size?.toLowerCase().includes(lowerSearch)
      );
    }

    // 狀態篩選
    if (status) {
      result = result.filter(device => device.status === status);
    }

    // 尺寸篩選
    if (size) {
      result = result.filter(device => device.data?.size === size);
    }

    setFilteredDevices(result);

    // 保持分頁狀態 - 檢查當前頁面是否仍然有效
    const newTotalPages = Math.ceil(result.length / itemsPerPage);
    if (currentPage > newTotalPages && newTotalPages > 0) {
      // 如果當前頁面超出了新的總頁數，調整到最後一頁
      console.log(`[分頁保持] 當前頁面 ${currentPage} 超出新總頁數 ${newTotalPages}，調整到最後一頁`);
      setCurrentPage(newTotalPages);
    }
    // 如果當前頁面仍然有效，則保持不變

    // 保持有效的選取狀態（只保留在篩選結果中的項目）
    if (selectedItems.length > 0) {
      const filteredDeviceIds = result.map(device => device._id || '').filter(id => id !== '');
      const validSelectedItems = selectedItems.filter(id => filteredDeviceIds.includes(id));

      if (validSelectedItems.length !== selectedItems.length) {
        console.log(`[篩選選取保持] 保持有效選取項目: ${selectedItems.length} -> ${validSelectedItems.length}`);
        setSelectedItems(validSelectedItems);
      }
    }
  };

  // 應用篩選器
  const applyFilters = (
    deviceList: Device[],
    search: string,
    status: DeviceStatus | '',
    size: string
  ) => {
    let result = [...deviceList];

    // 搜尋條件篩選 - 支援多欄位搜尋
    if (search) {
      const lowerSearch = search.toLowerCase();
      result = result.filter(
        device =>
          device.macAddress.toLowerCase().includes(lowerSearch) ||
          device.model?.toLowerCase().includes(lowerSearch) ||
          device.hardwareVersion?.toLowerCase().includes(lowerSearch) ||
          device.dataId?.toLowerCase().includes(lowerSearch) ||
          device.note?.toLowerCase().includes(lowerSearch) ||
          device.code?.toLowerCase().includes(lowerSearch) ||
          device.data?.size?.toLowerCase().includes(lowerSearch)
      );
    }

    // 狀態篩選
    if (status) {
      result = result.filter(device => device.status === status);
    }    // 尺寸篩選
    if (size) {
      result = result.filter(device => device.data?.size === size);
    }

    setFilteredDevices(result);

    // 重置分頁
    setCurrentPage(1);

    // 保持有效的選取狀態（只保留在篩選結果中的項目）
    if (selectedItems.length > 0) {
      const filteredDeviceIds = result.map(device => device._id || '').filter(id => id !== '');
      const validSelectedItems = selectedItems.filter(id => filteredDeviceIds.includes(id));

      if (validSelectedItems.length !== selectedItems.length) {
        console.log(`[篩選選取保持] 保持有效選取項目: ${selectedItems.length} -> ${validSelectedItems.length}`);
        setSelectedItems(validSelectedItems);
      }
    }
  };

  // 當篩選條件變更時重新應用篩選（重置分頁）
  useEffect(() => {
    applyFilters(devices, searchTerm, statusFilter, sizeFilter);
  }, [searchTerm, statusFilter, sizeFilter]);

  // 當設備數據變更時重新應用篩選（保持分頁）
  useEffect(() => {
    applyFiltersWithPaginationPreservation(devices, searchTerm, statusFilter, sizeFilter);
  }, [devices]);

  // 處理搜尋
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // 處理狀態篩選
  const handleStatusFilter = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStatusFilter(e.target.value as DeviceStatus | '' );
  };

  // 處理尺寸篩選
  const handleSizeFilter = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSizeFilter(e.target.value);
  };

  // 處理刪除設備
  const handleDelete = async (deviceId: string) => {
    if (window.confirm(t('devices.confirmDelete'))) {
      try {
        // 如果有門店ID，則傳遞給刪除函數
        await deleteDevice(deviceId, store?.id);
        showNotification('刪除設備成功', 'success');
        // 更新本地數據
        setDevices(prevDevices => prevDevices.filter(device => device._id !== deviceId));
      } catch (err: any) {
        showNotification(err.message || '刪除設備失敗', 'error');
      }
    }
  };

  // 處理批量刪除
  const handleBatchDelete = async () => {
    const count = selectedItems.length;
    if (count === 0) {
      showNotification('請先選擇要刪除的設備', 'error');
      return;
    }

    if (window.confirm(t('devices.confirmDeleteMultiple', { count }))) {
      try {
        // 逐個刪除選中的設備
        for (const deviceId of selectedItems) {
          // 如果有門店ID，則傳遞給刪除函數
          await deleteDevice(deviceId, store?.id);
        }

        showNotification(`成功刪除 ${count} 個設備`, 'success');

        // 更新本地數據
        setDevices(prevDevices =>
          prevDevices.filter(device => !selectedItems.includes(device._id || ''))
        );

        // 清空選擇
        setSelectedItems([]);
        setSelectAll(false);
      } catch (err: any) {
        showNotification(err.message || '批量刪除設備失敗', 'error');
      }
    }
  };

  // 處理批量發送
  const handleBatchSend = async () => {
    const count = selectedItems.length;
    if (count === 0) {
      showNotification('請先選擇要發送的設備', 'error');
      return;
    }

    try {
      // 先刷新前端數據，確保狀態處於最新
      showNotification('正在刷新設備狀態...', 'success', 1500);
      await fetchDevices();
    } catch (error) {
      console.error('刷新設備數據失敗:', error);
      showNotification('刷新設備數據失敗，將使用當前數據繼續', 'error', 2000);
    }

    // 獲取選中的設備（使用最新的設備數據）
    const selectedDevices = devices.filter(device =>
      selectedItems.includes(device._id || '')
    );

    // 過濾出需要更新的設備（狀態為"未更新"的設備）
    const devicesToUpdate = selectedDevices.filter(device =>
      device.imageUpdateStatus === '未更新'
    );

    if (devicesToUpdate.length === 0) {
      showNotification('選中的設備中沒有需要更新的設備（狀態為"未更新"）', 'error');
      return;
    }

    // 檢查是否有設備處於自動網關選擇模式
    const autoModeDevices = devicesToUpdate.filter(device =>
      device.gatewaySelectionMode === 'auto'
    );

    const hasAutoMode = autoModeDevices.length > 0;
    const confirmMessage = hasAutoMode
      ? `將發送 ${devicesToUpdate.length} 個未更新的設備，其中 ${autoModeDevices.length} 個設備啟用智能分配網關。是否繼續？`
      : `將發送 ${devicesToUpdate.length} 個未更新的設備。是否繼續？`;

    if (!window.confirm(confirmMessage)) {
      return;
    }

    try {
      setBatchSending(true);

      // 生成批量傳送ID
      const batchId = `batch_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      setCurrentBatchId(batchId);
      setShowBatchProgress(true);

      showNotification(`正在批量發送 ${devicesToUpdate.length} 個設備...`, 'success', 2000);

      // 使用批量發送API
      const deviceIds = devicesToUpdate.map(device => device._id!);
      const response = await sendMultipleDevicePreviewsToGateways(deviceIds, {
        sendToAllGateways: hasAutoMode, // 如果有智能分配設備，啟用發送到所有網關
        storeId: store?.id,
        concurrency: 3, // 並發數量
        enableSmartSelection: true, // 啟用智能網關選擇
        batchId: batchId // 傳遞批量ID用於進度追蹤
      });

      // 後端返回結構：{ success: boolean, message: string, result: { successCount, totalCount, ... } }
      const result = response.result;

      if (response.success) {
        // 構建詳細的成功訊息
        let message = `批量發送完成！成功發送 ${result.successCount}/${result.totalCount} 個設備`;

        // 添加智能選擇統計信息
        if (result.smartSelectionStats) {
          const stats = result.smartSelectionStats;
          if (stats.totalAutoModeDevices > 0) {
            message += `\n智能模式設備: ${stats.totalAutoModeDevices} 個`;
            if (stats.usedBackupGateway > 0) {
              message += `，使用備用網關: ${stats.usedBackupGateway} 個`;
            }
          }
        }

        showNotification(message, 'success', 8000);
      } else {
        // 構建詳細的部分成功訊息
        let message = `批量發送部分成功：${result.successCount}/${result.totalCount} 個設備發送成功`;

        // 添加智能選擇統計信息
        if (result.smartSelectionStats && result.smartSelectionStats.totalAutoModeDevices > 0) {
          const stats = result.smartSelectionStats;
          message += `\n智能模式設備: ${stats.totalAutoModeDevices} 個`;
          if (stats.usedBackupGateway > 0) {
            message += `，使用備用網關: ${stats.usedBackupGateway} 個`;
          }
        }

        showNotification(message, 'error', 10000);
      }

      // 重新獲取設備列表以更新狀態
      await fetchDevices();

      // 清空選擇
      setSelectedItems([]);
      setSelectAll(false);

    } catch (err: any) {
      showNotification(err.message || '批量發送失敗', 'error', 3000);  // 錯誤訊息顯示6秒
    } finally {
      setBatchSending(false);
      // 延遲隱藏進度顯示，讓用戶能看到最終結果
      setTimeout(() => {
        setShowBatchProgress(false);
        setCurrentBatchId(null);
      }, 3000);
    }
  };

  // 處理取消批量傳送
  const handleCancelBatchSend = () => {
    if (currentBatchId) {
      cancelBatchSend(currentBatchId);
      setBatchSending(false);
      setShowBatchProgress(false);
      setCurrentBatchId(null);
      showNotification('批量傳送已取消', 'success');
    }
  };

  // 處理關閉進度顯示
  const handleCloseBatchProgress = () => {
    setShowBatchProgress(false);
    setCurrentBatchId(null);
  };

  // 處理編輯設備
  const handleEdit = (device: Device) => {
    setSelectedDevice(device);
    setShowEditModal(true);
  };

  // 處理同步設備狀態
  const handleSync = async () => {
    try {
      setSyncingDevices(true);
      // 如果有門店ID，則傳遞給同步函數
      const success = await syncDevices(store?.id);
      if (success) {
        showNotification('設備狀態同步成功', 'success');
        // 重新獲取設備列表
        await fetchDevices();
      } else {
        showNotification('設備狀態同步失敗', 'error');
      }
    } catch (err: any) {
      showNotification(err.message || '設備狀態同步失敗', 'error');
    } finally {
      setSyncingDevices(false);
    }
  };
  // 處理發送預覽圖到網關
  const handleSendPreviewToGateway = async (device: Device) => {
    if (!device._id) {
      showNotification(t('devices.deviceIdMissing'), 'error');
      return;
    }

    // 檢查是否已經在發送中
    if (sendingDevices.has(device._id)) {
      showNotification('該設備正在發送中，請稍候', 'error');
      return;
    }

    try {
      // 添加到發送中的設備列表
      setSendingDevices(prev => new Set(prev).add(device._id!));

      // 確認設備有主要網關
      if (!device.primaryGatewayId) {
        showNotification(t('devices.noPrimaryGateway'), 'error');
        setSendingDevices(prev => {
          const newSet = new Set(prev);
          newSet.delete(device._id!);
          return newSet;
        });
        return;
      }

      // 先獲取最新的設備數據，確保我們使用最新的狀態
      let latestDevice: Device;
      try {
        const deviceResponse = await fetch(buildEndpointUrl(`devices/${device._id}`), {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
        });

        if (!deviceResponse.ok) {
          throw new Error('無法獲取最新設備數據');
        }

        latestDevice = await deviceResponse.json();
        console.log('已獲取最新設備數據:', latestDevice._id);
      } catch (error) {
        console.error('獲取最新設備數據失敗:', error);
        // 如果無法獲取最新數據，使用當前設備數據
        latestDevice = device;
      }

      // 嘗試獲取設備的模板和門店數據來重新生成預覽圖

      if (latestDevice.templateId && latestDevice.dataBindings) {
        showNotification('正在重新生成預覽圖...', 'success');

        try {
          // 1. 獲取模板數據
          const templateResponse = await fetch(buildEndpointUrl(`templates/${latestDevice.templateId}`), {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
          });

          if (!templateResponse.ok) {
            throw new Error('無法獲取模板數據');
          }

          const template = await templateResponse.json();

          // 2. 獲取門店數據
          const storeDataList = await getAllStoreData(latestDevice.storeId || store?.id || '');

          // 3. 使用 regeneratePreviewBeforeSend 函數重新生成預覽圖
          const newPreviewImage = await regeneratePreviewBeforeSend(latestDevice, storeDataList, template);

          if (newPreviewImage) {
            showNotification('已重新生成預覽圖，正在發送...', 'success');
          } else {
            // 如果重新生成失敗但設備有保存的預覽圖，則使用已有的預覽圖
            if (!latestDevice.previewImage) {
              showNotification('無法生成預覽圖，並且設備沒有保存的預覽圖', 'error');
              setSendingDevices(prev => {
                const newSet = new Set(prev);
                newSet.delete(device._id!);
                return newSet;
              });
              return;
            }
            showNotification('重新生成預覽圖失敗，將使用已保存的預覽圖', 'error');
          }
        } catch (e) {
          console.error('嘗試重新生成預覽圖失敗:', e);
          // 如果嘗試重新生成失敗但設備有保存的預覽圖，則使用已有的預覽圖
          if (!latestDevice.previewImage) {
            showNotification('無法生成預覽圖，並且設備沒有保存的預覽圖', 'error');
            setSendingDevices(prev => {
              const newSet = new Set(prev);
              newSet.delete(device._id!);
              return newSet;
            });
            return;
          }
          showNotification('重新生成預覽圖失敗，將使用已保存的預覽圖', 'error');
        }
      } else if (!latestDevice.previewImage) {
        // 如果無法重新生成且沒有預覽圖，則提示錯誤
        showNotification(t('devices.noPreviewImage'), 'error');
        setSendingDevices(prev => {
          const newSet = new Set(prev);
          newSet.delete(device._id!);
          return newSet;
        });
        return;
      }

      // 發送預覽圖到網關
      if (!latestDevice._id) {
        showNotification('設備ID不存在，無法發送', 'error');
        setSendingDevices(prev => {
          const newSet = new Set(prev);
          newSet.delete(device._id!);
          return newSet;
        });
        return;
      }

      console.log(`發送預覽圖到網關，設備ID: ${latestDevice._id}`);
      showNotification(`正在發送預覽圖到設備 ${device.macAddress}...`, 'success');

      const result = await sendDevicePreviewToGateway(latestDevice._id, {
        sendToAllGateways: false,
        storeId: latestDevice.storeId || store?.id
      });
      console.log(`發送預覽圖到網關結果:`, result);

      if (result.success) {
        if (result.skipped) {
          showNotification(`設備 ${device.macAddress} 圖片內容未變化，跳過發送`, 'success');
        } else {
          showNotification(`設備 ${device.macAddress} 預覽圖發送成功`, 'success');
        }
        // 發送成功後立即重新獲取設備列表，以更新圖片更新狀態
        await fetchDevices();
      } else {
        showNotification(`設備 ${device.macAddress} 預覽圖發送失敗: ${result.error || '未知錯誤'}`, 'error');
      }
    } catch (error) {
      console.error('發送預覽圖到網關出錯:', error);
      showNotification(`設備 ${device.macAddress} 預覽圖發送失敗: ${error instanceof Error ? error.message : '未知錯誤'}`, 'error');
    } finally {
      // 從發送中的設備列表移除
      setSendingDevices(prev => {
        const newSet = new Set(prev);
        newSet.delete(device._id!);
        return newSet;
      });
    }
  };

  // 顯示通知 - 使用泡泡通知
  const showNotification = (message: string, type: 'success' | 'error' = 'success', duration: number = 3000) => {
    if (type === 'success') {
      showSuccessNotification(message, duration);
    } else {
      showErrorNotification(message, duration);
    }
  };

  // 處理全選/取消全選
  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    const checked = e.target.checked;
    setSelectAll(checked);

    if (checked) {
      // 選中當前頁的所有設備
      const currentPageItems = getCurrentPageItems().map(device => device._id || '').filter(id => id !== '');
      // 合併當前頁的項目到已選取的項目中
      setSelectedItems(prev => {
        const newItems = [...prev];
        currentPageItems.forEach(id => {
          if (!newItems.includes(id)) {
            newItems.push(id);
          }
        });
        return newItems;
      });
    } else {
      // 取消選中當前頁的所有設備
      const currentPageItems = getCurrentPageItems().map(device => device._id || '').filter(id => id !== '');
      setSelectedItems(prev => prev.filter(id => !currentPageItems.includes(id)));
    }
  };

  // 處理單個選擇
  const handleSelectItem = (e: React.ChangeEvent<HTMLInputElement>, deviceId: string) => {
    const checked = e.target.checked;

    if (checked) {
      setSelectedItems(prev => [...prev, deviceId]);
    } else {
      setSelectedItems(prev => prev.filter(item => item !== deviceId));
    }
  };

  // 獲取當前頁的設備
  const getCurrentPageItems = () => {
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    return filteredDevices.slice(indexOfFirstItem, indexOfLastItem);
  };

  // 計算總頁數
  const totalPages = Math.ceil(filteredDevices.length / itemsPerPage);

  // 處理頁面變更
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // 監控選取狀態變化，自動更新全選狀態（只檢查當前頁）
  useEffect(() => {
    const currentPageItems = getCurrentPageItems();
    if (currentPageItems.length === 0) {
      setSelectAll(false);
      return;
    }

    // 檢查當前頁的所有設備是否都被選中
    const currentPageIds = currentPageItems.map(device => device._id || '').filter(id => id !== '');
    const allCurrentPageSelected = currentPageIds.length > 0 &&
      currentPageIds.every(id => selectedItems.includes(id));

    setSelectAll(allCurrentPageSelected);
  }, [selectedItems, currentPage, filteredDevices]);
  // 獲取唯一的尺寸列表
  const uniqueSizes = [...new Set(devices.map(device => device.data?.size).filter(Boolean))];
  // 渲染電量指示器
  const renderBatteryIndicator = (battery?: number) => {
    if (battery === undefined) {
      return <div className="flex items-center">
        <Battery className="w-4 h-4 mr-1 text-gray-400" />
        <span>未知</span>
      </div>;
    }

    let color = 'text-green-500';
    if (battery < 20) {
      color = 'text-red-500';
    } else if (battery < 50) {
      color = 'text-yellow-500';
    }

    return (
      <div className="flex items-center">
        <Battery className={`w-4 h-4 mr-1 ${color}`} />
        <span className={battery < 20 ? 'text-red-500 font-bold' : ''}>{battery}%</span>
      </div>
    );
  };

  // 渲染信號強度指示器
  const renderSignalStrength = (rssi?: number) => {
    if (rssi === undefined) {
      return <div className="flex items-center">
        <Wifi className="w-4 h-4 mr-1 text-gray-400" />
        <span>未知</span>
      </div>;
    }

    // RSSI 通常在 -100 (很弱) 到 0 (很強) 之間
    let icon = <Wifi className="w-4 h-4 mr-1 text-gray-400" />;
    let color = 'text-gray-500';

    if (rssi > -70) {
      icon = <Wifi className="w-4 h-4 mr-1 text-green-500" />;
      color = 'text-green-500';
    } else if (rssi > -85) {
      icon = <Wifi className="w-4 h-4 mr-1 text-yellow-500" />;
      color = 'text-yellow-500';
    } else {
      icon = <Wifi className="w-4 h-4 mr-1 text-red-500" />;
      color = 'text-red-500';
    }

    return (
      <div className="flex items-center">
        {icon}
        <span className={color}>{rssi} dBm</span>
      </div>
    );
  };

  // 渲染即時更新狀態指示器
  const renderRealTimeStatusIndicator = () => {
    const getStatusColor = () => {
      if (!isRealTimeEnabled) return 'text-gray-400';
      switch (realTimeConnectionStatus) {
        case 'connected': return 'text-green-500';
        case 'connecting': return 'text-yellow-500';
        case 'disconnected': return 'text-red-500';
        default: return 'text-gray-400';
      }
    };

    const getStatusText = () => {
      if (!isRealTimeEnabled) return '即時更新已關閉';
      switch (realTimeConnectionStatus) {
        case 'connected': return '即時更新已開啟';
        case 'connecting': return '正在連接...';
        case 'disconnected': return '連接已斷開';
        default: return '未知狀態';
      }
    };

    const getBgColor = () => {
      if (!isRealTimeEnabled) return 'bg-muted';
      switch (realTimeConnectionStatus) {
        case 'connected': return 'bg-green-50 dark:bg-green-900/20';
        case 'connecting': return 'bg-yellow-50 dark:bg-yellow-900/20';
        case 'disconnected': return 'bg-red-50 dark:bg-red-900/20';
        default: return 'bg-muted';
      }
    };

    const getBorderColor = () => {
      if (!isRealTimeEnabled) return 'border-border';
      switch (realTimeConnectionStatus) {
        case 'connected': return 'border-green-300 dark:border-green-700';
        case 'connecting': return 'border-yellow-300 dark:border-yellow-700';
        case 'disconnected': return 'border-red-300 dark:border-red-700';
        default: return 'border-border';
      }
    };

    return (
      <div className={`flex items-center gap-2 px-3 py-2 rounded-md border ${getBgColor()} ${getBorderColor()}`}>
        <div className={`w-2 h-2 rounded-full ${getStatusColor().replace('text-', 'bg-')}`} />
        <span className={`text-sm ${getStatusColor()}`}>
          {getStatusText()}
        </span>
        <button
          onClick={() => setIsRealTimeEnabled(!isRealTimeEnabled)}
          className="text-sm text-blue-600 hover:text-blue-800 underline ml-2"
        >
          {isRealTimeEnabled ? '關閉' : '開啟'}
        </button>
      </div>
    );
  };
  return (
    <div className="p-8 lg:px-4 py-2">
      <div className="max-w-7xl mx-auto">


        {/* 錯誤消息 */}
        {error && (
          <div className="mb-4 p-4 bg-red-100 border-l-4 border-red-500 text-red-700 flex items-center justify-between">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 mr-2" />
              <span>{error}</span>
            </div>
            <button
              className="text-gray-500 hover:text-gray-700"
              onClick={() => setError(null)}
            >
              &times;
            </button>
          </div>
        )}

        {/* 工具列 */}
        <div className="mb-6 flex flex-wrap items-center gap-4">
          {/* 顯示當前門店名稱 */}
          <div className="px-4 py-2 bg-blue-100 text-blue-800 rounded-md border border-blue-300">
            <span className="font-medium">{store?.name || '未選擇門店'}</span>
            <span className="ml-2 text-blue-600">({store?.id || ''})</span>
          </div>

          {/* 即時更新狀態指示器 */}
          {renderRealTimeStatusIndicator()}
        </div>

        {/* 設備狀態統計面板 */}
        <div className="mb-6 relative">
          {/* 玻璃效果背景 */}
          <div className="absolute inset-0 bg-gradient-to-br from-sky-100/80 via-blue-50/60 to-cyan-100/80 backdrop-blur-sm rounded-2xl border border-white/30 shadow-xl"></div>

          {/* 動態光效背景 */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent rounded-2xl animate-pulse opacity-50"></div>

          {/* 內容區域 */}
          <div className="relative p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-800 flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-sky-400 to-blue-600 rounded-lg flex items-center justify-center shadow-lg">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                設備狀態統計
              </h3>
              <div className="text-sm text-gray-600 bg-white/40 px-3 py-1 rounded-full border border-white/30">
                總計: {devices.length} 台設備
              </div>
            </div>

            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
              {/* 在線設備 */}
              <div
                className="group relative cursor-pointer"
                onClick={() => {
                  const onlineDeviceIds = filteredDevices
                    .filter(d => d.status === 'online')
                    .map(d => d._id || '')
                    .filter(id => id !== '');
                  setSelectedItems(onlineDeviceIds);
                }}
                title="點擊選取所有在線設備"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/20 to-green-500/20 rounded-xl border border-emerald-200/50 group-hover:shadow-lg group-hover:scale-105 transition-all duration-300"></div>
                <div className="relative p-4 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 to-green-500 rounded-lg flex items-center justify-center shadow-md">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-emerald-700 mb-1">
                    {devices.filter(d => d.status === 'online').length}
                  </div>
                  <div className="text-lg font-bold text-emerald-700">在線設備</div>
                </div>
              </div>

              {/* 離線設備 */}
              <div
                className="group relative cursor-pointer"
                onClick={() => {
                  const offlineDeviceIds = filteredDevices
                    .filter(d => d.status === 'offline')
                    .map(d => d._id || '')
                    .filter(id => id !== '');
                  setSelectedItems(offlineDeviceIds);
                }}
                title="點擊選取所有離線設備"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-gray-400/20 to-slate-500/20 rounded-xl border border-gray-200/50 group-hover:shadow-lg group-hover:scale-105 transition-all duration-300"></div>
                <div className="relative p-4 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <div className="w-10 h-10 bg-gradient-to-br from-gray-400 to-slate-500 rounded-lg flex items-center justify-center shadow-md">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-gray-700 mb-1">
                    {devices.filter(d => d.status === 'offline').length}
                  </div>
                  <div className="text-lg font-bold text-gray-700">離線設備</div>
                </div>
              </div>

              {/* 已更新圖片 */}
              <div
                className="group relative cursor-pointer"
                onClick={() => {
                  const updatedDeviceIds = filteredDevices
                    .filter(d => d.imageUpdateStatus === '已更新')
                    .map(d => d._id || '')
                    .filter(id => id !== '');
                  setSelectedItems(updatedDeviceIds);
                }}
                title="點擊選取所有已更新圖片的設備"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-sky-400/20 to-blue-500/20 rounded-xl border border-sky-200/50 group-hover:shadow-lg group-hover:scale-105 transition-all duration-300"></div>
                <div className="relative p-4 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <div className="w-10 h-10 bg-gradient-to-br from-sky-400 to-blue-500 rounded-lg flex items-center justify-center shadow-md">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-sky-700 mb-1">
                    {devices.filter(d => d.imageUpdateStatus === '已更新').length}
                  </div>
                  <div className="text-lg font-bold text-sky-700">已更新圖片</div>
                </div>
              </div>

              {/* 待更新圖片 */}
              <div
                className="group relative cursor-pointer"
                onClick={() => {
                  const pendingDeviceIds = filteredDevices
                    .filter(d => d.imageUpdateStatus === '未更新')
                    .map(d => d._id || '')
                    .filter(id => id !== '');
                  setSelectedItems(pendingDeviceIds);
                }}
                title="點擊選取所有待更新圖片的設備"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-amber-400/20 to-orange-500/20 rounded-xl border border-amber-200/50 group-hover:shadow-lg group-hover:scale-105 transition-all duration-300"></div>
                <div className="relative p-4 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <div className="w-10 h-10 bg-gradient-to-br from-amber-400 to-orange-500 rounded-lg flex items-center justify-center shadow-md">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-amber-700 mb-1">
                    {devices.filter(d => d.imageUpdateStatus === '未更新').length}
                  </div>
                  <div className="text-lg font-bold text-amber-700">待更新圖片</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 搜索和篩選工具列 */}
        <div className="mb-6 flex flex-wrap items-center gap-4">

          <div className="relative flex-1">
            <input
              type="text"
              placeholder={t('devices.searchPlaceholder')}
              value={searchTerm}
              onChange={handleSearch}
              className="w-full pl-4 pr-10 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <Search className="absolute right-3 top-2.5 text-gray-400 w-5 h-5" />
          </div>

          {/* 狀態篩選 */}
          <select
            value={statusFilter}
            onChange={handleStatusFilter}
            className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">{t('devices.allStatuses')}</option>
            <option value="online">{t('devices.online')}</option>
            <option value="offline">{t('devices.offline')}</option>
          </select>

          {/* 尺寸篩選 */}
          <select
            value={sizeFilter}
            onChange={handleSizeFilter}
            className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">{t('devices.allSizes')}</option>
            {uniqueSizes.map(size => (
              <option key={size} value={size}>{size}</option>
            ))}
          </select>

          {/* 同步按鈕 */}
          <button
            onClick={handleSync}
            disabled={syncingDevices}
            className={`flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md ${
              syncingDevices ? 'opacity-75 cursor-not-allowed' : 'hover:bg-blue-600'
            }`}
          >
            <RefreshCw className={`w-5 h-5 ${syncingDevices ? 'animate-spin' : ''}`} />
            {syncingDevices ? t('devices.syncing') : t('devices.syncDevices')}
          </button>

          {/* 全選所有按鈕 */}
          <button
            onClick={() => {
              const allFilteredDeviceIds = filteredDevices.map(device => device._id || '').filter(id => id !== '');
              const allSelected = allFilteredDeviceIds.length > 0 &&
                allFilteredDeviceIds.every(id => selectedItems.includes(id));

              if (allSelected) {
                // 取消全選所有
                setSelectedItems([]);
              } else {
                // 全選所有
                setSelectedItems(allFilteredDeviceIds);
              }
            }}
            className="flex items-center gap-2 px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600"
            title={
              filteredDevices.length > 0 &&
              filteredDevices.every(device => selectedItems.includes(device._id || ''))
                ? '取消全選所有設備'
                : '全選所有設備 (跨頁)'
            }
          >
            <CheckSquare className="w-5 h-5" />
            {filteredDevices.length > 0 &&
             filteredDevices.every(device => selectedItems.includes(device._id || ''))
              ? '取消全選所有'
              : '全選所有'
            }
          </button>

          {/* 批量發送按鈕 */}
          <button
            onClick={handleBatchSend}
            disabled={batchSending || selectedItems.length === 0}
            className={`flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-md ${
              batchSending || selectedItems.length === 0 ? 'opacity-75 cursor-not-allowed' : 'hover:bg-green-600'
            }`}
            title={selectedItems.length === 0 ? '請先選擇要發送的設備' : `批量發送 ${selectedItems.length} 個設備`}
          >
            <Send className={`w-5 h-5 ${batchSending ? 'animate-spin' : ''}`} />
            {batchSending ? '批量發送中...' : `批量發送${selectedItems.length > 0 ? ` (${selectedItems.length})` : ''}`}
          </button>

          {/* 批量刪除按鈕 */}
          <button
            onClick={handleBatchDelete}
            disabled={selectedItems.length === 0}
            className={`flex items-center gap-2 px-4 py-2 bg-red-500 text-white rounded-md ${
              selectedItems.length === 0 ? 'opacity-75 cursor-not-allowed' : 'hover:bg-red-600'
            }`}
            title={selectedItems.length === 0 ? '請先選擇要刪除的設備' : `批量刪除 ${selectedItems.length} 個設備`}
          >
            <Trash2 className="w-5 h-5" />
            {selectedItems.length > 0 ? `${t('common.delete')} (${selectedItems.length})` : t('common.delete')}
          </button>

          {/* 欄位管理器 */}
          <div className="relative" ref={fieldManagerRef}>
            <button
              className="flex items-center gap-2 px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600"
              onClick={() => setShowFieldManager(!showFieldManager)}
              ref={fieldManagerButtonRef}
            >
              <Grid className="w-5 h-5" />
              {t('devices.fieldManagement')}
            </button>

            {showFieldManager && (
              <div className="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg z-50 py-2 px-3 border border-gray-200">
                <div className="pb-2 border-b border-gray-200">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium text-sm text-gray-700">{t('database.displayFieldSettings')}</h3>
                    <div>
                      <label className="text-xs flex items-center text-gray-600 cursor-pointer">
                        <input
                          type="checkbox"
                          className="mr-1 rounded"
                          checked={areAllFieldsSelected()}
                          onChange={toggleAllFields}
                        />
                        {t('common.selectAll')}
                      </label>
                    </div>
                  </div>
                </div>                <div className="pt-2 max-h-60 overflow-y-auto">
                  {Object.keys(visibleFields).map((field) => (
                    <label key={field} className={`block py-1 px-2 text-sm ${field === 'macAddress' ? 'text-gray-500' : 'text-gray-700'} hover:bg-gray-100 rounded cursor-pointer`}>
                      <input
                        type="checkbox"
                        className="mr-2 rounded"
                        checked={visibleFields[field] || false}
                        onChange={() => toggleFieldVisibility(field)}
                        disabled={field === 'macAddress'}
                      />                      {field === 'macAddress' ? `${t('devices.macAddress')} (${t('common.required')})` :
                       field === 'model' ? t('devices.model') :
                       field === 'hardwareVersion' ? t('devices.hardwareVersion') :
                       field === 'firmwareVersion' ? '韌體版本' :
                       field === 'size' ? t('devices.size') :
                       field === 'colorType' ? t('devices.colorType') :
                       field === 'rssi' ? t('devices.rssi') :
                       field === 'battery' ? t('devices.battery') :
                       field === 'status' ? t('devices.status') :
                       field === 'dataId' ? t('devices.dataId') :
                       field === 'templateId' ? t('templates.templateName') :
                       field === 'imageUpdateStatus' ? '圖片更新狀態' :
                       field === 'lastSeen' ? t('devices.lastSeen') :
                       field === 'code' ? t('devices.code') :
                       field === 'note' ? t('devices.note') : field}
                    </label>
                  ))}

                </div>
              </div>
            )}
          </div>
        </div>        {/* 設備列表 */}
        <div className="bg-card rounded-lg shadow relative">
          {/* 使用三個固定位置的表格實現左右固定，中間滾動的效果 */}
          <div className="relative overflow-x-auto" style={{ maxWidth: "100%" }}>
            <div className="flex relative">
              {/* 左側固定欄位（勾選框和序號） */}
              <div className="sticky left-0 z-20 bg-card shadow-sm" style={{ width: "130px" }}>
                <table className="w-full border-separate border-spacing-0">
                  <thead>
                    <tr>
                      <th className="bg-amber-600 px-4 py-3 w-12 border-b border-gray-200 text-white">
                        <div className="flex flex-col items-center">
                          <input
                            type="checkbox"
                            className="rounded"
                            checked={selectAll}
                            onChange={handleSelectAll}
                            title={selectAll ? "取消當前頁全選" : "全選當前頁"}
                          />
                        </div>
                      </th>                      <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 text-white">
                        {t('database.serialNumber')}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan={2} className="bg-card px-4 py-8 text-center text-muted-foreground">
                          <div className="p-8">
                            <RefreshCw className="w-5 h-5 animate-spin mx-auto mb-2" />
                          </div>
                        </td>
                      </tr>
                    ) : getCurrentPageItems().length === 0 ? (
                      <tr>
                        <td colSpan={2} className="bg-card px-4 py-8 text-center text-muted-foreground">
                          <div className="p-8">{t('database.noData')}</div>
                        </td>
                      </tr>
                    ) : (
                      getCurrentPageItems().map((device, index) => (
                        <tr key={`left-${device._id || index}`} className={`h-[53px] ${index % 2 === 0 ? 'bg-amber-50 dark:bg-amber-900/20' : 'bg-blue-50 dark:bg-blue-900/20'}`}>
                          <td className={`px-4 py-3 border-b border-gray-200 align-middle`}>
                            <input
                              type="checkbox"
                              className="rounded"
                              checked={selectedItems.includes(device._id || '')}
                              onChange={(e) => handleSelectItem(e, device._id || '')}
                            />
                          </td>
                          <td className={`px-4 py-3 border-b border-gray-200 align-middle`}>
                            {/* 顯示流水號，從1開始 */}
                            {(currentPage - 1) * itemsPerPage + index + 1}
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {/* 中間可滾動區域（動態欄位） */}
              <div className="flex-1 overflow-x-auto">
                <table className="w-full border-separate border-spacing-0">
                  <thead>
                    <tr>
                      {visibleFields.macAddress && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('devices.macAddress')}
                        </th>
                      )}
                      {visibleFields.model && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[120px] text-white">
                          {t('devices.model')}
                        </th>
                      )}
                      {visibleFields.hardwareVersion && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('devices.hardwareVersion')}
                        </th>
                      )}
                      {visibleFields.firmwareVersion && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          韌體版本
                        </th>
                      )}
                      {visibleFields.size && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[80px] text-white">
                          {t('devices.size')}
                        </th>
                      )}
                      {visibleFields.colorType && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[100px] text-white">
                          {t('devices.colorType')}
                        </th>
                      )}
                      {visibleFields.rssi && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[120px] text-white">
                          {t('devices.rssi')}
                        </th>
                      )}
                      {visibleFields.battery && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[100px] text-white">
                          {t('devices.battery')}
                        </th>
                      )}
                      {visibleFields.status && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[80px] text-white">
                          {t('devices.status')}
                        </th>
                      )}
                      {visibleFields.imageUpdateStatus && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[120px] text-white">
                          圖片更新狀態
                        </th>
                      )}
                                            {visibleFields.dataId && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('devices.dataId')}
                        </th>
                      )}
                      {visibleFields.templateId && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('templates.templateName')}
                        </th>
                      )}                      {visibleFields.lastSeen && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[180px] text-white">
                          {t('devices.lastSeen')}
                        </th>
                      )}
                      {visibleFields.code && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[100px] text-white">
                          {t('devices.code')}
                        </th>
                      )}                      {visibleFields.note && (
                        <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap min-w-[150px] text-white">
                          {t('devices.note')}
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan={Object.values(visibleFields).filter(Boolean).length} className="bg-card px-4 py-8 text-center text-muted-foreground">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : getCurrentPageItems().length === 0 ? (
                      <tr>
                        <td colSpan={Object.values(visibleFields).filter(Boolean).length} className="bg-card px-4 py-8 text-center text-muted-foreground">
                          <div className="p-8">
                            {filteredDevices.length === 0 && devices.length > 0
                              ? t('devices.noFilteredDevices')
                              : t('devices.noDevices')}
                          </div>
                        </td>
                      </tr>
                    ) : (
                      getCurrentPageItems().map((device, index) => (
                        <tr
                          key={`mid-${device._id || index}`}
                          className={`h-[53px] ${index % 2 === 0 ? 'bg-amber-50 dark:bg-amber-900/20' : 'bg-blue-50 dark:bg-blue-900/20'} cursor-pointer hover:bg-amber-100 dark:hover:bg-amber-800/30`}
                          onClick={() => onViewDeviceDetail && onViewDeviceDetail(device)}
                        >
                          {visibleFields.macAddress && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {device.gatewaySelectionMode === 'auto' ? (
                                <div className="smart-gateway-mac-container">
                                  <div className="smart-gateway-wave-border">
                                    <span className="smart-gateway-mac-text">
                                      {device.macAddress}
                                    </span>
                                  </div>
                                </div>
                              ) : (
                                <div className="smart-gateway-mac-container">
                                  {device.macAddress}
                                </div>
                              )}
                            </td>
                          )}
                          {visibleFields.model && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[120px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {device.model || '未知'}
                            </td>
                          )}
                          {visibleFields.hardwareVersion && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {device.hardwareVersion || '未知'}
                            </td>
                          )}
                          {visibleFields.firmwareVersion && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {device.firmwareVersion || '未知'}
                            </td>
                          )}
                          {visibleFields.size && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[80px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {device.data?.size || '未知'}
                            </td>
                          )}
                          {visibleFields.colorType && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[140px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              <ColorTypeGradient colorType={device.data?.colorType || '未知'} size="sm" />
                            </td>
                          )}
                          {visibleFields.rssi && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[120px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {renderSignalStrength(device.data?.rssi)}
                            </td>
                          )}
                          {visibleFields.battery && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[100px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {renderBatteryIndicator(device.data?.battery)}
                            </td>
                          )}{visibleFields.status && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[80px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              <DeviceStatusBadge status={device.status} />
                            </td>
                          )}                          {visibleFields.imageUpdateStatus && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[120px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>                              <ImageUpdateStatusBadge
                                deviceImageCode={device.data?.imageCode}
                                updateStatus={device.imageUpdateStatus}
                                size="sm"
                              />
                            </td>
                          )}
                          {visibleFields.dataId && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[200px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {(() => {
                                // 顯示所有綁定數據
                                if (device.dataBindings) {
                                  try {
                                    // 解析數據綁定
                                    const bindings = typeof device.dataBindings === 'string'
                                      ? JSON.parse(device.dataBindings)
                                      : device.dataBindings;

                                    // 將綁定數據值去重
                                    const uniqueValues = [...new Set(Object.values(bindings))];

                                    // 從門店數據中查找對應的ID值
                                    const displayValues = uniqueValues.map(uid => {
                                      // 嘗試在門店數據中查找對應的項目
                                      const storeItem = storeData.find(item =>
                                        item.uid === uid ||
                                        item._uid === uid ||
                                        (item._id && item._id.toString() === uid)
                                      );

                                      // 如果找到了對應的項目，顯示其ID或名稱
                                      if (storeItem) {
                                        if (storeItem.name) {
                                          return storeItem.name;
                                        } else if (storeItem.id) {
                                          return storeItem.id;
                                        }
                                      }

                                      // 如果是UID格式（24位十六進制字符串），不顯示
                                      const uidPattern = /^[0-9a-f]{24}$/i;
                                      if (typeof uid === 'string' && uidPattern.test(uid)) {
                                        return ''; // 不顯示UID
                                      }

                                      // 如果都不符合，顯示原始值
                                      return uid;
                                    }).filter(Boolean); // 過濾掉空字符串

                                    // 如果沒有有效的顯示值，顯示默認值
                                    if (displayValues.length === 0) {
                                      return device.dataId || '-';
                                    }

                                    return displayValues.join(', ');
                                  } catch (e) {
                                    return device.dataId || '-';
                                  }
                                } else {
                                  return device.dataId || '-';
                                }
                              })()}
                            </td>
                          )}
                          {visibleFields.templateId && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {device.templateId ? (templateMap[device.templateId] || device.templateId) : '-'}
                            </td>
                          )}{visibleFields.lastSeen && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[180px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {device.lastSeen
                                ? new Date(device.lastSeen).toLocaleString()
                                : '無記錄'}
                            </td>
                          )}
                          {visibleFields.code && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[100px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {device.code || '-'}
                            </td>
                          )}                          {visibleFields.note && (
                            <td className={`px-4 py-3 border-b border-gray-200 min-w-[150px] align-middle whitespace-nowrap overflow-hidden text-ellipsis`}>
                              {device.note || '-'}
                            </td>
                          )}
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {/* 右側固定區域（操作欄） */}
              <div className="sticky right-0 z-20 bg-white shadow-sm" style={{ width: "180px" }}>
                <table className="w-full border-separate border-spacing-0">
                  <thead>
                    <tr>
                      <th className="bg-amber-600 px-4 py-3 text-left border-b border-gray-200 whitespace-nowrap text-white">
                        {t('devices.actions')}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : getCurrentPageItems().length === 0 ? (
                      <tr>
                        <td className="bg-white px-4 py-8 text-center text-gray-500">
                          <div className="p-8"></div>
                        </td>
                      </tr>
                    ) : (
                      getCurrentPageItems().map((device, index) => (
                        <tr key={`right-${device._id || index}`} className={`h-[53px] align-middle ${index % 2 === 0 ? 'bg-amber-50' : 'bg-blue-50'}`}>
                          <td className="px-4 py-3 border-b border-gray-200 whitespace-nowrap overflow-hidden text-ellipsis">
                            <div className="flex items-center gap-2">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation(); // 防止觸發行點擊事件
                                  handleSendPreviewToGateway(device);
                                }}
                                disabled={sendingDevices.has(device._id || '')}
                                className={`${
                                  sendingDevices.has(device._id || '')
                                    ? 'text-gray-400 cursor-not-allowed'
                                    : 'text-gray-500 hover:text-green-600'
                                }`}
                                title={
                                  sendingDevices.has(device._id || '')
                                    ? '發送中...'
                                    : t('devices.sendPreview')
                                }
                              >
                                {sendingDevices.has(device._id || '') ? (
                                  <RefreshCw className="w-5 h-5 animate-spin" />
                                ) : (
                                  <Send className="w-5 h-5" />
                                )}
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation(); // 防止觸發行點擊事件
                                  setSelectedDevice(device);
                                  setShowBindModal(true);
                                }}
                                className="text-gray-500 hover:text-blue-600"
                                title={t('devices.bindData')}
                              >
                                <Link className="w-5 h-5" />
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation(); // 防止觸發行點擊事件
                                  handleEdit(device);
                                }}
                                className="text-gray-500 hover:text-blue-600"
                                title={t('devices.editDevice')}
                              >
                                <Edit className="w-5 h-5" />
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation(); // 防止觸發行點擊事件
                                  handleDelete(device._id || '');
                                }}
                                className="text-gray-500 hover:text-red-600"
                                title={t('common.delete')}
                              >
                                <Trash className="w-5 h-5" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          {/* 分頁控制 */}
          {filteredDevices.length > 0 && (
            <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200">
              <div className="flex flex-col gap-1">
                <p className="text-sm text-gray-700">
                  {t('common.showing')}
                  <span className="font-medium mx-1">
                    {filteredDevices.length === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1}
                  </span>
                  {t('common.to')}
                  <span className="font-medium mx-1">
                    {Math.min(currentPage * itemsPerPage, filteredDevices.length)}
                  </span>
                  {t('common.of')}
                  <span className="font-medium mx-1">{filteredDevices.length}</span>
                  {t('common.entries')}
                </p>
                {/* 選取狀態信息 */}
                {selectedItems.length > 0 && (
                  <p className="text-sm text-blue-600 font-medium">
                    已選取 {selectedItems.length} 個設備
                    {selectedItems.length > itemsPerPage && ' (跨頁選取)'}
                  </p>
                )}
              </div>

              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  {/* 上一頁按鈕 */}
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">{t('common.previousPage')}</span>
                    &laquo;
                  </button>

                  {/* 頁碼按鈕 */}
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(pageNum => (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium
                        ${currentPage === pageNum
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                    >
                      {pageNum}
                    </button>
                  ))}

                  {/* 下一頁按鈕 */}
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">{t('common.nextPage')}</span>
                    &raquo;
                  </button>
                </nav>
              </div>
            </div>
          )}
        </div>
      </div>



      {/* 編輯設備模態窗口 */}
      <EditDeviceModal
        isOpen={showEditModal}
        device={selectedDevice}
        storeId={store?.id}
        onClose={() => {
          setShowEditModal(false);
          setSelectedDevice(null);
        }}
        onSuccess={() => {
          fetchDevices();
          showNotification('設備更新成功', 'success');
        }}
      />

      {/* 綁定數據模態窗口 */}
      <BindDeviceDataModal
        isOpen={showBindModal}
        device={selectedDevice}
        store={store} // 明確傳遞當前門店信息
        onClose={() => {
          setShowBindModal(false);
          setSelectedDevice(null);
        }}
        onSuccess={() => {
          fetchDevices();
          showNotification('數據綁定成功', 'success');
        }}
      />

      {/* 批量傳送進度顯示 */}
      <BatchSendProgress
        isVisible={showBatchProgress}
        onClose={handleCloseBatchProgress}
        onCancel={batchSending ? handleCancelBatchSend : undefined}
        batchId={currentBatchId || undefined}
      />
    </div>
  );
}
