import React, { useState, useRef } from 'react';
import { X, Upload, AlertCircle, CheckCircle, FileText, HardDrive } from 'lucide-react';
import { Button } from '../ui/button';
import { uploadSoftware, validateBinFile, ValidationReport, checkSoftwareNameExists, checkVersionDeviceFunctionExists } from '../../utils/api/softwareApi';

interface SoftwareUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function SoftwareUploadModal({ isOpen, onClose, onSuccess }: SoftwareUploadModalProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [validationReport, setValidationReport] = useState<ValidationReport | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    tags: [] as string[]
  });
  const [uploading, setUploading] = useState(false);
  const [validating, setValidating] = useState(false);
  const [checkingName, setCheckingName] = useState(false);
  const [nameExists, setNameExists] = useState(false);
  const [checkingVersion, setCheckingVersion] = useState(false);
  const [versionExists, setVersionExists] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 當模態框打開時重置表單
  React.useEffect(() => {
    if (isOpen) {
      resetForm();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  // 處理檔案選擇
  const handleFileSelect = async (file: File) => {
    setSelectedFile(file);
    setValidationReport(null);
    setError(null);

    // 自動填入軟體名稱（如果還沒有輸入）
    if (!formData.name) {
      const fileName = file.name.replace('.bin', '');
      setFormData(prev => ({
        ...prev,
        name: fileName
      }));
      // 檢查自動填入的軟體名稱是否已存在
      checkNameExists(fileName);
    }

    // 驗證檔案
    try {
      setValidating(true);
      const report = await validateBinFile(file);
      setValidationReport(report);

      // 如果驗證成功，檢查版本組合是否已存在
      if (report.status === 'valid' && report.details) {
        checkVersionExists(report.details.version, report.details.deviceType, report.details.functionType);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '檔案驗證失敗');
    } finally {
      setValidating(false);
    }
  };

  // 處理拖拽
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    const binFile = files.find(file => file.name.toLowerCase().endsWith('.bin'));
    
    if (binFile) {
      handleFileSelect(binFile);
    } else {
      setError('請選擇.bin檔案');
    }
  };

  // 處理檔案輸入
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };



  // 檢查軟體名稱是否已存在
  const checkNameExists = async (name: string) => {
    if (!name.trim()) {
      setNameExists(false);
      return;
    }

    try {
      setCheckingName(true);
      const exists = await checkSoftwareNameExists(name.trim());
      setNameExists(exists);
    } catch (err) {
      console.error('檢查軟體名稱失敗:', err);
      // 檢查失敗時不阻止用戶繼續操作
      setNameExists(false);
    } finally {
      setCheckingName(false);
    }
  };

  // 檢查版本+設備類型+功能類型組合是否已存在
  const checkVersionExists = async (version: string, deviceType: string, functionType: string) => {
    try {
      setCheckingVersion(true);
      const exists = await checkVersionDeviceFunctionExists(version, deviceType, functionType);
      setVersionExists(exists);
    } catch (err) {
      console.error('檢查軟體版本組合失敗:', err);
      // 檢查失敗時不阻止用戶繼續操作
      setVersionExists(false);
    } finally {
      setCheckingVersion(false);
    }
  };

  // 處理軟體名稱變更
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setFormData(prev => ({ ...prev, name: newName }));

    // 延遲檢查名稱是否存在
    if (newName.trim()) {
      const timeoutId = setTimeout(() => {
        checkNameExists(newName);
      }, 500); // 500ms 延遲

      return () => clearTimeout(timeoutId);
    } else {
      setNameExists(false);
    }
  };



  // 處理表單提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedFile) {
      setError('請選擇要上傳的檔案');
      return;
    }

    if (!validationReport || validationReport.status !== 'valid') {
      setError('檔案驗證失敗，無法上傳');
      return;
    }

    if (!formData.name.trim()) {
      setError('請輸入軟體名稱');
      return;
    }

    if (nameExists) {
      setError('軟體名稱已存在，請使用其他軟體名稱');
      return;
    }

    if (versionExists) {
      setError('相同版本的軟體已存在');
      return;
    }

    try {
      setUploading(true);
      setError(null);
      
      await uploadSoftware(selectedFile, formData);
      onSuccess();
    } catch (err) {
      setError(err instanceof Error ? err.message : '上傳失敗');
    } finally {
      setUploading(false);
    }
  };

  // 重置表單
  const resetForm = () => {
    setSelectedFile(null);
    setValidationReport(null);
    setFormData({
      name: '',
      description: '',
      tags: []
    });
    setError(null);
    setNameExists(false);
    setCheckingName(false);
    setVersionExists(false);
    setCheckingVersion(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 處理關閉
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // 獲取驗證狀態圖標
  const getValidationIcon = () => {
    if (validating) {
      return <div className="animate-spin w-5 h-5 border-2 border-orange-500 border-t-transparent rounded-full" />;
    }
    
    if (!validationReport) return null;
    
    switch (validationReport.status) {
      case 'valid':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
      case 'unsupported':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* 標題列 */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">上傳軟體</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* 檔案上傳區域 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              選擇bin檔案 *
            </label>
            <div
              className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                dragOver
                  ? 'border-orange-500 bg-orange-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              {selectedFile ? (
                <div className="space-y-2">
                  <FileText className="w-12 h-12 text-orange-500 mx-auto" />
                  <p className="text-sm font-medium text-gray-900">{selectedFile.name}</p>
                  <p className="text-xs text-gray-500">
                    {selectedFile.size >= 1024 * 1024
                      ? `${(selectedFile.size / 1024 / 1024).toFixed(2)} MB`
                      : selectedFile.size >= 1024
                        ? `${(selectedFile.size / 1024).toFixed(1)} KB`
                        : `${selectedFile.size} bytes`
                    }
                  </p>

                  {/* 檔案驗證狀態 */}
                  <div className="flex items-center justify-center gap-2">
                    {getValidationIcon()}
                    {validationReport && (
                      <span className={`text-sm ${
                        validationReport.status === 'valid' ? 'text-green-600' :
                        validationReport.status === 'error' ? 'text-red-600' :
                        'text-yellow-600'
                      }`}>
                        {validationReport.message}
                      </span>
                    )}
                  </div>

                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    className="text-sm text-orange-600 hover:text-orange-700"
                  >
                    選擇其他檔案
                  </button>
                </div>
              ) : (
                <div className="space-y-2">
                  <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                  <p className="text-sm text-gray-600">
                    拖拽bin檔案到此處，或
                    <button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      className="text-orange-600 hover:text-orange-700 ml-1"
                    >
                      點擊選擇檔案
                    </button>
                  </p>
                  <p className="text-xs text-gray-500">支援最大100MB的.bin檔案</p>
                </div>
              )}
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept=".bin"
              onChange={handleFileInputChange}
              className="hidden"
            />
          </div>

          {/* 驗證結果詳細資訊 */}
          {validationReport && validationReport.details && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-900 mb-3">檔案資訊</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">設備類型:</span>
                  <span className="ml-2 font-medium">{validationReport.details.deviceType}</span>
                </div>
                <div>
                  <span className="text-gray-600">模組:</span>
                  <span className="ml-2 font-medium">{validationReport.details.model}</span>
                </div>
                <div>
                  <span className="text-gray-600">功能類型:</span>
                  <span className="ml-2 font-medium">{validationReport.details.functionType}</span>
                </div>
                <div>
                  <span className="text-gray-600">韌體版本:</span>
                  <span className="ml-2 font-medium">{validationReport.details.version}</span>
                </div>
                <div>
                  <span className="text-gray-600">最小硬體版本:</span>
                  <span className="ml-2 font-medium">{validationReport.details.minHwVersion}</span>
                </div>
                <div>
                  <span className="text-gray-600">最大硬體版本:</span>
                  <span className="ml-2 font-medium">{validationReport.details.maxHwVersion}</span>
                </div>
                <div>
                  <span className="text-gray-600">韌體大小:</span>
                  <span className="ml-2 font-medium">
                    {validationReport.details.binSize >= 1024
                      ? `${(validationReport.details.binSize / 1024).toFixed(1)} KB`
                      : `${validationReport.details.binSize} bytes`
                    }
                  </span>
                </div>
                <div className="col-span-2">
                  <span className="text-gray-600">CRC校驗:</span>
                  <span className="ml-2 font-mono text-xs">{validationReport.details.checksum}</span>
                </div>
              </div>

              {/* 版本組合檢查狀態 */}
              <div className="mt-4 pt-3 border-t border-gray-200">
                <div className="flex items-center gap-2">
                  {checkingVersion ? (
                    <>
                      <div className="animate-spin w-4 h-4 border-2 border-orange-500 border-t-transparent rounded-full" />
                      <span className="text-sm text-gray-600">檢查版本組合中...</span>
                    </>
                  ) : versionExists ? (
                    <>
                      <AlertCircle className="w-4 h-4 text-red-500" />
                      <span className="text-sm text-red-600">
                        版本 {validationReport.details.version} 的 {validationReport.details.deviceType} {validationReport.details.functionType} 軟體已存在
                      </span>
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm text-green-600">版本組合可用</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 軟體資訊表單 */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                軟體名稱 *
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={formData.name}
                  onChange={handleNameChange}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                    nameExists ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  placeholder="輸入軟體名稱"
                  required
                />
                {checkingName && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <div className="animate-spin w-4 h-4 border-2 border-orange-500 border-t-transparent rounded-full" />
                  </div>
                )}
              </div>
              {nameExists && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  軟體名稱已存在，請使用其他軟體名稱
                </p>
              )}
              {formData.name.trim() && !checkingName && !nameExists && (
                <p className="mt-1 text-sm text-green-600 flex items-center">
                  <CheckCircle className="w-4 h-4 mr-1" />
                  軟體名稱可用
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                軟體描述
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="輸入軟體描述"
                rows={3}
              />
            </div>


          </div>

          {/* 錯誤提示 */}
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
              <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
              <span className="text-red-700">{error}</span>
            </div>
          )}

          {/* 操作按鈕 */}
          <div className="flex gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={uploading}
              className="flex-1"
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={uploading || !selectedFile || !validationReport || validationReport.status !== 'valid' || nameExists || checkingName || versionExists || checkingVersion}
              className="flex-1 bg-orange-500 hover:bg-orange-600 text-white"
            >
              {uploading ? (
                <>
                  <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                  上傳中...
                </>
              ) : (
                <>
                  <Upload className="w-4 h-4 mr-2" />
                  上傳軟體
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
