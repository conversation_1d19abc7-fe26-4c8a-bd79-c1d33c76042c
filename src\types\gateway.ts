// WebSocket連接信息
export interface WebSocketInfo {
  url: string;               // WebSocket連接URL
  path: string;              // WebSocket連接路徑
  token: string;             // 認證Token
  protocol: string;          // 協議類型
}

// 網關接口定義
export interface Gateway {
  _id?: string;              // MongoDB ID
  name: string;              // 網關名稱
  macAddress: string;        // MAC地址
  status: GatewayStatus;     // 狀態
  lastSeen: Date | null;     // 最後更新時間
  model: string;             // 模組
  hardwareVersion: string;   // 硬體版本 (x.x.x.x格式)
  wifiFirmwareVersion: string; // WiFi固件版本
  btFirmwareVersion: string;   // 藍芽固件版本
  ipAddress: string;         // IP地址
  storeId: string;           // 所屬門店ID
  createdAt: Date | null;    // 創建時間
  updatedAt: Date | null;    // 更新時間
  note?: string;             // 備註
  websocket?: WebSocketInfo; // WebSocket連接信息
}

// 網關狀態類型
export type GatewayStatus = 'online' | 'offline';

// 網關篩選選項
export interface GatewayFilter {
  status?: GatewayStatus;
  model?: string;
  search?: string;
}
