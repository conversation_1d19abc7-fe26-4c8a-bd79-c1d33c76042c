import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Bo<PERSON>, Key, Settings, TestTube, Save, AlertCircle, CheckCircle, Loader } from 'lucide-react';
import { getAIConfig, saveAIConfig, testAIConnection, AIConfig } from '../../utils/api/aiAssistantApi';

// AIConfig interface is now imported from aiAssistantApi

const DEFAULT_AI_CONFIG: AIConfig = {
  geminiApiKey: '',
  enabled: false,
  model: 'gemini-2.0-flash',
  maxTokens: 2048,
  temperature: 0.7,
  timeout: 30000
};

export function AISettingsTab() {
  const { t } = useTranslation();
  const [config, setConfig] = useState<AIConfig>(DEFAULT_AI_CONFIG);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);

  // 載入AI配置
  useEffect(() => {
    loadAIConfig();
  }, []);

  const loadAIConfig = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const aiConfig = await getAIConfig();
      setConfig({ ...DEFAULT_AI_CONFIG, ...aiConfig });
    } catch (err) {
      console.error('載入AI配置失敗:', err);
      // 如果配置不存在，使用默認配置
      setConfig(DEFAULT_AI_CONFIG);
    } finally {
      setIsLoading(false);
    }
  };

  // 保存AI配置
  const handleSave = async () => {
    try {
      setIsSaving(true);
      setError(null);
      setSuccess(null);

      // 驗證API Key格式
      if (config.enabled && config.geminiApiKey && !config.geminiApiKey.startsWith('AIza')) {
        throw new Error('Gemini API Key格式不正確，應以"AIza"開頭');
      }

      await saveAIConfig(config);
      setSuccess('AI設定已保存');
      
      // 3秒後清除成功訊息
      setTimeout(() => setSuccess(null), 3000);
    } catch (err: any) {
      console.error('保存AI配置失敗:', err);
      setError(err.message || '保存AI配置失敗，請重試');
    } finally {
      setIsSaving(false);
    }
  };

  // 測試AI連接
  const handleTestConnection = async () => {
    try {
      setIsTesting(true);
      setTestResult(null);
      setError(null);

      if (!config.geminiApiKey) {
        throw new Error('請先輸入Gemini API Key');
      }

      if (!config.geminiApiKey.startsWith('AIza')) {
        throw new Error('API Key格式不正確，應該以"AIza"開頭');
      }

      console.log('開始測試AI連接...');

      // 先保存當前配置並啟用AI功能
      const testConfig = { ...config, enabled: true };
      console.log('保存測試配置:', { ...testConfig, geminiApiKey: '***' });

      await saveAIConfig(testConfig);
      console.log('配置保存成功，開始測試連接...');

      // 等待一小段時間確保配置已保存
      await new Promise(resolve => setTimeout(resolve, 500));

      // 測試AI連接
      const result = await testAIConnection();
      console.log('測試結果:', result);

      if (result.success) {
        setTestResult({ success: true, message: 'AI服務連接成功！' });
        // 更新本地配置狀態
        setConfig(testConfig);
      } else {
        setTestResult({ success: false, message: result.message || 'AI服務連接失敗' });
      }
    } catch (err: any) {
      console.error('測試AI連接失敗:', err);
      setTestResult({ success: false, message: err.message || '測試連接失敗' });
    } finally {
      setIsTesting(false);
    }
  };

  // 更新配置字段
  const updateConfig = (field: keyof AIConfig, value: any) => {
    setConfig(prev => ({ ...prev, [field]: value }));
    setError(null);
    setSuccess(null);
    setTestResult(null);
  };

  if (isLoading) {
    return (
      <div className="p-6 flex items-center justify-center">
        <Loader className="animate-spin h-8 w-8 text-blue-500" />
        <span className="ml-2 text-gray-600">載入AI設定中...</span>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-8">
      {/* 標題 */}
      <div className="flex items-center space-x-3">
        <Bot className="h-8 w-8 text-blue-500" />
        <div>
          <h2 className="text-2xl font-bold text-gray-900">EPD Agent 設定</h2>
          <p className="text-gray-600">配置基於Gemini的EPD Agent功能</p>
        </div>
      </div>

      {/* 錯誤和成功訊息 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-2">
          <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
          <span className="text-red-700">{error}</span>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center space-x-2">
          <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
          <span className="text-green-700">{success}</span>
        </div>
      )}

      {/* 基本設定 */}
      <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
        <div className="flex items-center mb-4">
          <div className="w-2 h-6 bg-blue-500 rounded-full mr-3"></div>
          <h3 className="text-lg font-semibold text-gray-800">基本設定</h3>
        </div>

        <div className="space-y-6">
          {/* 啟用AI助手 */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700">啟用AI助手</label>
              <p className="text-sm text-gray-500">開啟或關閉AI助手功能</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={config.enabled}
                onChange={(e) => updateConfig('enabled', e.target.checked)}
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          {/* Gemini API Key */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Key className="inline h-4 w-4 mr-1" />
              Gemini API Key
            </label>
            <div className="relative">
              <input
                type="password"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm pr-10"
                placeholder="AIzaSy..."
                value={config.geminiApiKey}
                onChange={(e) => updateConfig('geminiApiKey', e.target.value)}
              />
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                <Key className="h-4 w-4 text-gray-400" />
              </div>
            </div>
            <p className="mt-1 text-xs text-gray-500">
              請到 <a href="https://makersuite.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Google AI Studio</a> 獲取API Key
            </p>
          </div>
        </div>
      </div>

      {/* 高級設定 */}
      <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
        <div className="flex items-center mb-4">
          <div className="w-2 h-6 bg-green-500 rounded-full mr-3"></div>
          <h3 className="text-lg font-semibold text-gray-800">高級設定</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* AI模組 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">AI模組</label>
            <select
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm"
              value={config.model}
              onChange={(e) => updateConfig('model', e.target.value)}
            >
              <option value="gemini-2.0-flash">Gemini 2.0 Flash</option>
              <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
              <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
            </select>
          </div>

          {/* 最大Token數 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              最大Token數 ({config.maxTokens})
            </label>
            <input
              type="range"
              min="100"
              max="8192"
              step="100"
              className="w-full"
              value={config.maxTokens}
              onChange={(e) => updateConfig('maxTokens', parseInt(e.target.value))}
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>100</span>
              <span>8192</span>
            </div>
          </div>

          {/* 創造性溫度 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              創造性溫度 ({config.temperature})
            </label>
            <input
              type="range"
              min="0"
              max="2"
              step="0.1"
              className="w-full"
              value={config.temperature}
              onChange={(e) => updateConfig('temperature', parseFloat(e.target.value))}
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>0 (保守)</span>
              <span>2 (創新)</span>
            </div>
          </div>

          {/* 請求超時 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              請求超時 ({config.timeout / 1000}秒)
            </label>
            <input
              type="range"
              min="5000"
              max="120000"
              step="5000"
              className="w-full"
              value={config.timeout}
              onChange={(e) => updateConfig('timeout', parseInt(e.target.value))}
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>5秒</span>
              <span>120秒</span>
            </div>
          </div>
        </div>
      </div>

      {/* 測試結果 */}
      {testResult && (
        <div className={`rounded-lg p-4 border ${
          testResult.success 
            ? 'bg-green-50 border-green-200' 
            : 'bg-red-50 border-red-200'
        }`}>
          <div className="flex items-center space-x-2">
            {testResult.success ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <AlertCircle className="h-5 w-5 text-red-500" />
            )}
            <span className={testResult.success ? 'text-green-700' : 'text-red-700'}>
              {testResult.message}
            </span>
          </div>
        </div>
      )}

      {/* 操作按鈕 */}
      <div className="flex space-x-4">
        <button
          onClick={handleSave}
          disabled={isSaving}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isSaving ? (
            <Loader className="animate-spin h-4 w-4 mr-2" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          {isSaving ? '保存中...' : '保存設定'}
        </button>

        <button
          onClick={handleTestConnection}
          disabled={isTesting || !config.geminiApiKey}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isTesting ? (
            <Loader className="animate-spin h-4 w-4 mr-2" />
          ) : (
            <TestTube className="h-4 w-4 mr-2" />
          )}
          {isTesting ? '測試中...' : '測試連接'}
        </button>
      </div>
    </div>
  );
}
